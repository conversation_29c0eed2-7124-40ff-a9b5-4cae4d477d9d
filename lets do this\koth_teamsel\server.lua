print('[KOTH] Server loading...')

-- MONEY AND XP SYSTEM - Define at top so all handlers can access it
local playerData = {} -- Cache for player data
print(('[KOTH] DEBUG: playerData initialized as %s'):format(type(playerData)))

local teamSpawns = {
  red =    { x = 2238.15, y = 3788.91, z = 35.89, heading = 120.5 },
  blue =   { x = 1323.77, y = 3143.33, z = 40.41, heading = 282.48 },
  green =  { x = 1865.99, y = 2607.15, z = 45.67, heading = 276.45 }
}

-- Team player tracking
local playerTeams = {} -- Track each player's team
local teamCounts = { red = 0, blue = 0, green = 0 } -- Live team counts
local zonePoints = { red = 0, blue = 0, green = 0 } -- Zone control points

-- Function to update team counts and broadcast to all clients
function UpdateTeamCounts()
  -- Count actual players in each team
  local actualCounts = { red = 0, blue = 0, green = 0 }

  for playerId, team in pairs(playerTeams) do
    if GetPlayerPing(playerId) > 0 then -- Player is still connected
      actualCounts[team] = (actualCounts[team] or 0) + 1
    end
  end

  -- Update global team counts
  teamCounts = actualCounts

  -- Broadcast to all clients for HUD update
  TriggerClientEvent('koth:updateTeamCounts', -1, teamCounts)

  print(('[KOTH] Team counts updated - Red: %d, Blue: %d, Green: %d'):format(
    teamCounts.red, teamCounts.blue, teamCounts.green
  ))
end

-- Function to get player's team
function GetPlayerTeam(playerId)
  return playerTeams[playerId]
end

-- Function to update zone points and broadcast to all clients
function UpdateZonePoints()
  -- Broadcast zone points to all clients
  TriggerClientEvent('koth:updateZonePoints', -1, zonePoints)

  print(('[KOTH] Zone points updated - Red: %d, Green: %d, Blue: %d'):format(
    zonePoints.red, zonePoints.green, zonePoints.blue
  ))
end

-- Function to award zone points to a team
function AwardZonePoints(team, points)
  if team and zonePoints[team] then
    zonePoints[team] = zonePoints[team] + points
    UpdateZonePoints()

    print(('[KOTH] Awarded %d points to %s team (Total: %d)'):format(
      points, team, zonePoints[team]
    ))
  end
end

-- Team selection handler (no persistence)
RegisterNetEvent('koth:pickTeam', function(team)
  local src = source
  print(('[KOTH] Player %d selected team: %s'):format(src, team or 'none'))

  local spawn = teamSpawns[team]
  if spawn then
    -- Update player's team (session only)
    playerTeams[src] = team

    -- Spawn player
    TriggerClientEvent('koth:spawnPlayer', src, spawn)
    print(('[KOTH] Spawning player %d at team %s'):format(src, team))

    -- Update team counts for all players
    UpdateTeamCounts()
  else
    print(('[KOTH] Invalid team: %s'):format(team or 'none'))
  end
end)

-- Team count request (for team selection screen)
RegisterNetEvent('koth:requestCounts', function()
  local src = source
  print(('[KOTH] Player %d requested team counts'):format(src))

  -- Always show team selection (no persistence)
  TriggerClientEvent('koth:updateCounts', src, teamCounts)
end)

-- Vehicle purchase handlers
RegisterNetEvent('koth:buyVehicle', function(data)
  local src = source
  local vehicleName = type(data) == 'table' and data.name or data
  local price = tonumber(type(data) == 'table' and data.price or 0)

  print(('[KOTH] Player %d wants to buy: %s (price: $%d)'):format(src, vehicleName or 'none', price))

  if not vehicleName or not price or price <= 0 then
    TriggerClientEvent('koth:purchaseResult', src, false, 'Invalid purchase data')
    return
  end

  -- Check if player has enough money
  if not playerData[src] then
    print(('[KOTH] Player data not loaded for %d, attempting to load...'):format(src))
    LoadPlayerData(src)

    Citizen.SetTimeout(500, function()
      if not playerData[src] then
        print(('[KOTH] Failed to load player data for %d after retry'):format(src))
        TriggerClientEvent('koth:purchaseResult', src, false, 'Player data not loaded. Please try again.')
      else
        print(('[KOTH] Player data loaded for %d, retrying vehicle purchase'):format(src))
        TriggerEvent('koth:buyVehicle', data)
      end
    end)
    return
  end

  if playerData[src].money < price then
    TriggerClientEvent('koth:purchaseResult', src, false, 'Not enough money')
    print(('[KOTH] Player %d cannot afford %s ($%d) - has $%d'):format(src, vehicleName, price, playerData[src].money))
    return
  end

  -- Deduct money and save to database
  playerData[src].money = playerData[src].money - price
  SavePlayerData(src)

  print(('[KOTH] Player %d bought %s for $%d - remaining: $%d'):format(src, vehicleName, price, playerData[src].money))

  -- Update client HUD with new money
  TriggerClientEvent('koth:updatePlayerData', src, playerData[src])

  -- Spawn the vehicle on server-side for networking
  SpawnVehicleForPlayer(src, vehicleName, 'buy', price)
  TriggerClientEvent('koth:purchaseResult', src, true, ('Purchased %s for $%d'):format(vehicleName, price))
end)

RegisterNetEvent('koth:rentVehicle', function(data)
  local src = source
  local vehicleName = type(data) == 'table' and data.name or data
  local price = tonumber(type(data) == 'table' and data.price or 0)

  print(('[KOTH] Player %d wants to rent: %s (price: $%d)'):format(src, vehicleName or 'none', price))

  if not vehicleName or not price or price <= 0 then
    TriggerClientEvent('koth:purchaseResult', src, false, 'Invalid rental data')
    return
  end

  -- Check if player has enough money
  if not playerData[src] then
    print(('[KOTH] Player data not loaded for %d, attempting to load...'):format(src))
    LoadPlayerData(src)

    Citizen.SetTimeout(500, function()
      if not playerData[src] then
        print(('[KOTH] Failed to load player data for %d after retry'):format(src))
        TriggerClientEvent('koth:purchaseResult', src, false, 'Player data not loaded. Please try again.')
      else
        print(('[KOTH] Player data loaded for %d, retrying vehicle rental'):format(src))
        TriggerEvent('koth:rentVehicle', data)
      end
    end)
    return
  end

  if playerData[src].money < price then
    TriggerClientEvent('koth:purchaseResult', src, false, 'Not enough money')
    print(('[KOTH] Player %d cannot afford to rent %s ($%d) - has $%d'):format(src, vehicleName, price, playerData[src].money))
    return
  end

  -- Deduct money and save to database
  playerData[src].money = playerData[src].money - price
  SavePlayerData(src)

  print(('[KOTH] Player %d rented %s for $%d - remaining: $%d'):format(src, vehicleName, price, playerData[src].money))

  -- Update client HUD with new money
  TriggerClientEvent('koth:updatePlayerData', src, playerData[src])

  -- Spawn the vehicle on server-side for networking
  SpawnVehicleForPlayer(src, vehicleName, 'rent', price)
  TriggerClientEvent('koth:purchaseResult', src, true, ('Rented %s for $%d'):format(vehicleName, price))
end)

-- Server-side vehicle spawning function for networking
function SpawnVehicleForPlayer(playerId, vehicleName, purchaseType, price)
  print(('[KOTH] Server spawning vehicle: %s for player %d'):format(vehicleName, playerId))
  
  -- Get player position
  local playerPed = GetPlayerPed(playerId)
  if not playerPed or playerPed == 0 then
    print(('[KOTH] Could not get player ped for %d'):format(playerId))
    return
  end
  
  local playerCoords = GetEntityCoords(playerPed)
  local playerHeading = GetEntityHeading(playerPed)
  
  -- Vehicle name mapping
  local vehicleModels = {
    ['Blista'] = 'blista',
    ['Futo'] = 'futo',
    ['Sultan'] = 'sultan',
    ['Elegy'] = 'elegy2',
    ['Kuruma'] = 'kuruma',
    ['Armored Kuruma'] = 'kuruma2',
    ['Insurgent'] = 'insurgent',
    ['Technical'] = 'technical',
    ['Sandking XL'] = 'sandking',
    ['Mesa'] = 'mesa',
    ['Buzzard'] = 'buzzard2',
    ['Savage'] = 'savage',
    ['Rhino Tank'] = 'rhino',
    ['Hydra'] = 'hydra'
  }
  
  local modelName = vehicleModels[vehicleName] or vehicleName:lower()
  local modelHash = GetHashKey(modelName)
  
  -- Calculate spawn position (in front of player)
  local spawnX = playerCoords.x + math.cos(math.rad(playerHeading)) * 5.0
  local spawnY = playerCoords.y + math.sin(math.rad(playerHeading)) * 5.0
  local spawnZ = playerCoords.z + 1.0
  
  -- Create networked vehicle
  local vehicle = CreateVehicle(modelHash, spawnX, spawnY, spawnZ, playerHeading, true, true)
  
  if vehicle and vehicle ~= 0 then
    -- Set vehicle properties for networking
    SetEntityAsMissionEntity(vehicle, true, true)
    SetVehicleHasBeenOwnedByPlayer(vehicle, true)
    
    -- Wait a moment for the vehicle to be created
    Citizen.SetTimeout(100, function()
      -- Put player in vehicle
      SetPedIntoVehicle(playerPed, vehicle, -1)
      
      -- Notify all clients about the new vehicle
      TriggerClientEvent('koth:vehicleSpawned', -1, {
        vehicle = vehicle,
        owner = playerId,
        vehicleName = vehicleName,
        purchaseType = purchaseType,
        price = price
      })
      
      print(('[KOTH] Successfully spawned networked %s for player %d'):format(vehicleName, playerId))
    end)
  else
    print(('[KOTH] Failed to create vehicle %s for player %d'):format(vehicleName, playerId))
  end
end

-- Add new server events for vehicle shop money requests
RegisterNetEvent('koth:getMoneyForVehicleShop', function(vehicles)
  local src = source
  local playerName = GetPlayerName(src)
  
  print(('[KOTH] Vehicle shop money request from %s'):format(playerName))
  
  -- Get fresh player data
  if playerData[src] and playerData[src].money then
    local playerMoney = playerData[src].money
    print(('[KOTH] Using cached data - Player %s has $%d'):format(playerName, playerMoney))
    
    TriggerClientEvent('koth:showVehicleShopWithMoney', src, {
      vehicles = vehicles,
      money = playerMoney
    })
  else
    print(('[KOTH] No cached data for %s, loading from database...'):format(playerName))
    LoadPlayerData(src)
    
    -- Wait for data to load then send
    Citizen.SetTimeout(1000, function()
      local money = playerData[src] and playerData[src].money or 0
      TriggerClientEvent('koth:showVehicleShopWithMoney', src, {
        vehicles = vehicles,
        money = money
      })
    end)
  end
end)

RegisterNetEvent('koth:getDataForClassShop', function(classes)
  local src = source
  local playerName = GetPlayerName(src)
  
  print(('[KOTH] Class shop data request from %s'):format(playerName))
  
  -- Get fresh player data
  if playerData[src] and playerData[src].money then
    local playerMoney = playerData[src].money
    local playerLevel = playerData[src].level or 1
    print(('[KOTH] Using cached data - Player %s has $%d, Level %d'):format(playerName, playerMoney, playerLevel))
    
    TriggerClientEvent('koth:showClassShopWithData', src, {
      classes = classes,
      money = playerMoney,
      level = playerLevel
    })
  else
    print(('[KOTH] No cached data for %s, loading from database...'):format(playerName))
    LoadPlayerData(src)
    
    -- Wait for data to load then send
    Citizen.SetTimeout(1000, function()
      local money = playerData[src] and playerData[src].money or 0
      local level = playerData[src] and playerData[src].level or 1
      TriggerClientEvent('koth:showClassShopWithData', src, {
        classes = classes,
        money = money,
        level = level
      })
    end)
  end
end)

-- Class selection handler (stub)
RegisterNetEvent('koth:selectClass', function(classId)
  local src = source
  print(('[KOTH] Player %d selected class: %s'):format(src, classId or 'none'))
  -- TODO: Add loadout/class logic
end)

-- Weapon/loadout selection handler
RegisterNetEvent('koth:selectLoadout', function(classId, weapon, price)
  local src = source
  local weaponPrice = tonumber(price or 0)

  print(('[KOTH] Player %d selected weapon %s for class %s (price: $%d)'):format(src, weapon or 'none', classId or 'none', weaponPrice))
  print(('[KOTH] DEBUG: playerData type: %s, playerData[%d] exists: %s'):format(type(playerData), src, tostring(playerData[src] ~= nil)))

  if not weapon or not classId then
    TriggerClientEvent('koth:purchaseResult', src, false, 'Invalid weapon selection')
    return
  end

  -- Ensure playerData table exists
  if not playerData then
    print('[KOTH] CRITICAL ERROR: playerData table is nil!')
    playerData = {}
  end

  -- Check if player has enough money (free weapons are allowed)
  if weaponPrice > 0 then
    print(('[KOTH] DEBUG: Checking money for player %d, playerData[%d] = %s'):format(src, src, tostring(playerData[src])))

    if not playerData[src] then
      print(('[KOTH] Player data not loaded for %d, attempting to load...'):format(src))
      -- Try to load player data immediately
      LoadPlayerData(src)

      -- Give it a moment to load and then check again
      Citizen.SetTimeout(1000, function()
        if not playerData[src] then
          print(('[KOTH] Failed to load player data for %d after retry'):format(src))
          TriggerClientEvent('koth:purchaseResult', src, false, 'Player data not loaded. Please try again.')
        else
          -- Retry the weapon purchase now that data is loaded
          print(('[KOTH] Player data loaded for %d, retrying weapon purchase'):format(src))
          TriggerEvent('koth:selectLoadout', classId, weapon, price)
        end
      end)
      return
    end

    if not playerData[src].money then
      print(('[KOTH] Player %d has invalid money data, playerData[%d] = %s'):format(src, src, json.encode(playerData[src])))
      TriggerClientEvent('koth:purchaseResult', src, false, 'Invalid player data. Please try again.')
      return
    end

    local currentMoney = tonumber(playerData[src].money) or 0
    print(('[KOTH] Player %d money check: has $%d, needs $%d'):format(src, currentMoney, weaponPrice))

    if currentMoney < weaponPrice then
      TriggerClientEvent('koth:purchaseResult', src, false, 'Not enough money')
      print(('[KOTH] Player %d cannot afford weapon %s ($%d) - has $%d'):format(src, weapon, weaponPrice, currentMoney))
      return
    end

    -- Deduct money and save to database
    playerData[src].money = currentMoney - weaponPrice
    SavePlayerData(src)

    print(('[KOTH] Player %d bought weapon %s for $%d - remaining: $%d'):format(src, weapon, weaponPrice, playerData[src].money))

    -- Update client HUD with new money
    TriggerClientEvent('koth:updatePlayerData', src, playerData[src])
  end

  -- Give the player the selected weapon
  TriggerClientEvent('koth:giveWeapon', src, weapon, classId, weaponPrice)

  if weaponPrice > 0 then
    TriggerClientEvent('koth:purchaseResult', src, true, ('Purchased %s for $%d'):format(weapon:gsub('WEAPON_', ''):gsub('_', ' '), weaponPrice))
  end
end)

-- DATABASE CONFIGURATION
-- Configured for your Zap Hosting MySQL database
local DB_CONFIG = {
  host = "mysql-mariadb-oce02-11-101.zap-srv.com",
  port = 3306,
  database = "zap1190649-1",
  username = "zap1190649-1",
  password = "tYXDWyEmvqykO75w"
}

-- MONEY AND XP SYSTEM
-- playerData moved to top of file

-- Get player identifiers
function GetPlayerIdentifiers(source)
  local identifiers = {
    txid = nil,
    steam = nil,
    discord = nil
  }

  for i = 0, GetNumPlayerIdentifiers(source) - 1 do
    local id = GetPlayerIdentifier(source, i)

    if string.find(id, "steam:") then
      identifiers.steam = id
    elseif string.find(id, "discord:") then
      identifiers.discord = id
    end
  end

  -- Use license as primary identifier (TX Admin compatible)
  identifiers.txid = GetPlayerIdentifierByType(source, 'license') or identifiers.steam or ('temp_' .. tostring(source))

  return identifiers
end

-- Load player data from database (no team restoration)
function LoadPlayerData(source)
  print(('[KOTH] LoadPlayerData called with source: %s (type: %s)'):format(tostring(source), type(source)))

  local identifiers = GetPlayerIdentifiers(source)
  local playerName = GetPlayerName(source)

  if not identifiers.txid then
    print(('[KOTH] ERROR: Could not get identifier for player %d'):format(source))
    return
  end

  -- Load from database
  print(('[KOTH] Attempting to load player data for %s (TXID: %s)'):format(playerName, identifiers.txid))

  exports.oxmysql:execute('SELECT * FROM koth_players WHERE txid = ?', {identifiers.txid}, function(result)
    print(('[KOTH] Database query completed for %s, result type: %s'):format(playerName, type(result)))

    if result then
      print(('[KOTH] Database result length: %d'):format(#result))
      if result[1] then
        print(('[KOTH] Found existing player data: Money=%s, XP=%s, Level=%s'):format(
          tostring(result[1].money), tostring(result[1].xp), tostring(result[1].level)))
      end
    end

    if result and result[1] then
      -- Player exists, load data
      playerData[source] = result[1]
      print(('[KOTH] Loaded existing player data for %s - Money: $%d, XP: %d, Level: %d'):format(
        playerName, result[1].money, result[1].xp, result[1].level))

      -- Send data immediately
      TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
      print(('[KOTH] Sent existing player data to client for %s'):format(playerName))

    else
      -- New player, create record
      print(('[KOTH] Creating new player record for %s'):format(playerName))
      exports.oxmysql:execute('INSERT INTO koth_players (txid, steam_id, discord_id, player_name, money, xp, level) VALUES (?, ?, ?, ?, ?, ?, ?)', {
        identifiers.txid,
        identifiers.steam,
        identifiers.discord,
        playerName,
        1000,
        0,
        1
      }, function(insertId)
        -- Create local data
        playerData[source] = {
          id = insertId,
          txid = identifiers.txid,
          steam_id = identifiers.steam,
          discord_id = identifiers.discord,
          player_name = playerName,
          money = 1000,
          xp = 0,
          level = 1,
          kills = 0,
          deaths = 0,
          zone_kills = 0
        }

        print(('[KOTH] Created new player record for %s - Money: $%d, XP: %d, Level: %d'):format(
          playerName, 1000, 0, 1))

        -- Send data immediately
        TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
        print(('[KOTH] Sent new player data to client for %s'):format(playerName))
      end)
    end
  end)

  -- Fallback: Create local data if database fails
  Citizen.SetTimeout(10000, function() -- Wait 10 seconds
    if not playerData[source] then
      print(('[KOTH] Database failed, creating fallback data for %s'):format(playerName))
      playerData[source] = {
        txid = identifiers.txid,
        steam_id = identifiers.steam,
        discord_id = identifiers.discord,
        player_name = playerName,
        money = 1000,
        xp = 0,
        level = 1,
        kills = 0,
        deaths = 0,
        zone_kills = 0
      }
      TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
    end
  end)
end

-- Save player data to database (no team saving)
function SavePlayerData(source)
  if not playerData[source] then return end

  local data = playerData[source]

  exports.oxmysql:execute('UPDATE koth_players SET money = ?, xp = ?, level = ?, kills = ?, deaths = ?, zone_kills = ?, player_name = ? WHERE txid = ?', {
    data.money,
    data.xp,
    data.level,
    data.kills,
    data.deaths,
    data.zone_kills,
    data.player_name,
    data.txid
  }, function(result)
    if result and (type(result) == 'number' and result > 0) or (type(result) == 'table' and result.affectedRows and result.affectedRows > 0) then
      print(('[KOTH] Saved data for player %s'):format(data.player_name))
    end
  end)
end

-- Player data request event
RegisterNetEvent('koth:requestPlayerData', function()
  local source = source
  print(('[KOTH] Player data requested by %s'):format(GetPlayerName(source)))

  if playerData[source] then
    TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
    print(('[KOTH] Sent requested player data to %s'):format(GetPlayerName(source)))
  else
    print(('[KOTH] No player data available for %s, loading from database...'):format(GetPlayerName(source)))
    LoadPlayerData(source)
  end
end)

-- Direct money request for weapon shop (guaranteed fix)
RegisterNetEvent('koth:getMoneyForWeaponShop', function(classId, weapons)
  local source = source
  local playerName = GetPlayerName(source)

  print(('[KOTH] Direct money request for weapon shop by %s'):format(playerName))

  -- Ensure player data is loaded
  if not playerData[source] then
    print(('[KOTH] Player data not loaded for %d, loading now...'):format(source))
    LoadPlayerData(source)
    
    -- Wait a moment for data to load
    Citizen.SetTimeout(500, function()
      if playerData[source] then
        TriggerClientEvent('koth:showWeaponShopWithMoney', source, {
          class = classId,
          weapons = weapons,
          money = playerData[source].money or 0
        })
      else
        -- Fallback if data still not loaded
        TriggerClientEvent('koth:showWeaponShopWithMoney', source, {
          class = classId,
          weapons = weapons,
          money = 0
        })
      end
    end)
    return
  end

  -- Send data immediately if available
  TriggerClientEvent('koth:showWeaponShopWithMoney', source, {
    class = classId,
    weapons = weapons,
    money = playerData[source].money or 0
  })
end)

-- Player connecting event
AddEventHandler('playerConnecting', function()
  local source = source
  print(('[KOTH] playerConnecting event - source: %s (type: %s)'):format(tostring(source), type(source)))
end)

-- Player joined event (better for loading data)
AddEventHandler('playerJoining', function()
  local source = source
  print(('[KOTH] playerJoining event - source: %s'):format(tostring(source)))
  -- Load data immediately
  LoadPlayerData(source)
end)

-- Also load on player spawn for reliability
RegisterNetEvent('playerSpawned', function()
  local source = source
  if not playerData[source] then
    print(('[KOTH] Player spawned without data, loading for: %s'):format(tostring(source)))
    LoadPlayerData(source)
  end
end)

-- Backup: Resource start event for existing players
AddEventHandler('onResourceStart', function(resourceName)
  if GetCurrentResourceName() ~= resourceName then return end

  print('[KOTH] Resource started, loading data for existing players...')
  -- Load immediately for all players
  for _, playerId in ipairs(GetPlayers()) do
    print(('[KOTH] Loading data for existing player: %s'):format(playerId))
    LoadPlayerData(tonumber(playerId))
  end
end)

-- KOTH ZONE SYSTEM
local kothZone = {
  controllingTeam = nil, -- 'red', 'blue', 'green', or nil for neutral
  captureProgress = 0.0, -- 0.0 to 100.0
  captureRate = 1.0, -- Points per second
  captureThreshold = 100.0, -- Points needed to capture
  decayRate = 0.5, -- Points lost per second when no players present
  contestedDecayRate = 0.25, -- Points lost per second when contested
  playersInZone = {
    red = 0,
    blue = 0,
    green = 0
  },
  dominantTeam = nil, -- Team with most players
  isContested = false -- True when multiple teams present
}

-- Track players in KOTH zone
RegisterNetEvent('koth:playerEnteredZone', function(team)
  local source = source
  if not team then return end

  -- Increment player count for team
  kothZone.playersInZone[team] = (kothZone.playersInZone[team] or 0) + 1
  print(('[KOTH] Player %s entered zone (Team: %s)'):format(source, team))

  -- Update zone status
  UpdateKothZoneStatus()
end)

RegisterNetEvent('koth:playerLeftZone', function(team)
  local source = source
  if not team then return end

  -- Decrement player count for team
  if kothZone.playersInZone[team] and kothZone.playersInZone[team] > 0 then
    kothZone.playersInZone[team] = kothZone.playersInZone[team] - 1
  end
  print(('[KOTH] Player %s left zone (Team: %s)'):format(source, team))

  -- Update zone status
  UpdateKothZoneStatus()
end)

-- Clean up when player disconnects
AddEventHandler('playerDropped', function()
  local source = source
  local playerTeam = GetPlayerTeam(source)

  if playerTeam then
    -- Remove from team tracking
    playerTeams[source] = nil
    print(('[KOTH] Player %s disconnected from team %s'):format(source, playerTeam))

    -- Update team counts
    UpdateTeamCounts()

    -- Remove from KOTH zone if present
    if kothZone.playersInZone[playerTeam] and kothZone.playersInZone[playerTeam] > 0 then
      kothZone.playersInZone[playerTeam] = kothZone.playersInZone[playerTeam] - 1
      print(('[KOTH] Player %s removed from KOTH zone count'):format(source))
      UpdateKothZoneStatus()
    end
  end

  -- Save player data and clean up
  SavePlayerData(source)
  playerData[source] = nil
end)

-- Update KOTH zone status (dominant team, contested status)
function UpdateKothZoneStatus()
  local maxPlayers = 0
  local dominantTeam = nil
  local isContested = false
  local teamsPresent = 0

  -- Find team with most players
  for team, count in pairs(kothZone.playersInZone) do
    if count > 0 then
      teamsPresent = teamsPresent + 1

      if count > maxPlayers then
        maxPlayers = count
        dominantTeam = team
      elseif count == maxPlayers and maxPlayers > 0 then
        -- If tie, zone is contested
        isContested = true
      end
    end
  end

  -- Zone is contested if multiple teams present
  isContested = teamsPresent > 1

  -- Update zone status
  kothZone.dominantTeam = dominantTeam
  kothZone.isContested = isContested

  print(('[KOTH] Zone status updated - Dominant team: %s, Contested: %s'):format(
    dominantTeam or 'none',
    isContested and 'yes' or 'no'
  ))
end

-- KOTH capture processing loop
Citizen.CreateThread(function()
  while true do
    -- Process capture logic
    if kothZone.dominantTeam then
      if kothZone.isContested then
        -- Zone is contested - progress decays
        if kothZone.captureProgress > 0 then
          kothZone.captureProgress = math.max(0, kothZone.captureProgress - kothZone.contestedDecayRate)
        end
      else
        -- Dominant team is capturing
        if kothZone.controllingTeam == nil or kothZone.controllingTeam ~= kothZone.dominantTeam then
          -- Capturing from neutral or enemy team
          kothZone.captureProgress = kothZone.captureProgress + kothZone.captureRate

          -- Check if capture complete
          if kothZone.captureProgress >= kothZone.captureThreshold then
            -- Zone captured
            local oldTeam = kothZone.controllingTeam
            kothZone.controllingTeam = kothZone.dominantTeam
            kothZone.captureProgress = 0

            -- Award points to capturing team
            AwardZonePoints(kothZone.controllingTeam, 1)

            -- Announce capture
            print(('[KOTH] Zone captured by %s team!'):format(kothZone.controllingTeam))
            TriggerClientEvent('koth:zoneControlChanged', -1, kothZone.controllingTeam)

            -- Notify all players
            local message = ('The KOTH zone has been captured by the %s team! (+1 point)'):format(kothZone.controllingTeam:upper())
            TriggerClientEvent('chat:addMessage', -1, {
              color = {255, 255, 0},
              multiline = true,
              args = {'KOTH', message}
            })
          end
        end
      end
    else
      -- No teams in zone - progress decays
      if kothZone.captureProgress > 0 then
        kothZone.captureProgress = math.max(0, kothZone.captureProgress - kothZone.decayRate)
      end
    end

    -- Broadcast zone status to all clients
    TriggerClientEvent('koth:updateZoneStatus', -1, {
      controllingTeam = kothZone.controllingTeam,
      captureProgress = kothZone.captureProgress,
      captureThreshold = kothZone.captureThreshold,
      dominantTeam = kothZone.dominantTeam,
      isContested = kothZone.isContested,
      playersInZone = kothZone.playersInZone
    })

    Citizen.Wait(1000) -- Update every second
  end
end)

-- PVP SYSTEM SERVER-SIDE
AddEventHandler('playerConnecting', function()
  local source = source

  -- Enable PVP for connecting player
  Citizen.SetTimeout(5000, function() -- Wait for player to fully load
    TriggerClientEvent('koth:enablePVP', source)
  end)
end)

-- Enable PVP for all players when resource starts
AddEventHandler('onResourceStart', function(resourceName)
  if GetCurrentResourceName() ~= resourceName then return end

  print('[KOTH] Enabling PVP for all players...')

  -- Enable PVP for all currently connected players
  for _, playerId in ipairs(GetPlayers()) do
    TriggerClientEvent('koth:enablePVP', playerId)
  end
end)

-- PVP enable event
RegisterNetEvent('koth:enablePVP', function()
  local source = source
  print(('[KOTH] PVP enabled for player %d'):format(source))
end)

-- Debug command to check player stats
RegisterCommand('checkstats', function(source, args, rawCommand)
  if source == 0 then
    print('[KOTH] This command can only be used by players')
    return
  end

  if not playerData[source] then
    TriggerClientEvent('chat:addMessage', source, {
      color = {255, 0, 0},
      multiline = true,
      args = {"[KOTH]", "Player data not loaded! Try rejoining the server."}
    })
    print(('[KOTH] Player %d requested stats but data not loaded'):format(source))
    return
  end

  local data = playerData[source]
  TriggerClientEvent('chat:addMessage', source, {
    color = {0, 255, 0},
    multiline = true,
    args = {"[KOTH Stats]", string.format("Money: $%d | XP: %d | Level: %d | Kills: %d | Deaths: %d",
      data.money, data.xp, data.level, data.kills, data.deaths)}
  })

  print(('[KOTH] Player %d (%s) stats - Money: $%d | XP: %d | Level: %d'):format(source, data.player_name, data.money, data.xp, data.level))
end, false)

-- Calculate level from XP
function CalculateLevel(xp)
  local levels = {
    {level = 1, required = 0},
    {level = 2, required = 100},
    {level = 3, required = 250},
    {level = 4, required = 500},
    {level = 5, required = 1000},
    {level = 6, required = 1750},
    {level = 7, required = 2750},
    {level = 8, required = 4000},
    {level = 9, required = 6000},
    {level = 10, required = 8500}
  }

  local currentLevel = 1
  for _, levelData in ipairs(levels) do
    if xp >= levelData.required then
      currentLevel = levelData.level
    else
      break
    end
  end

  return currentLevel
end

-- Award money and XP for kills
function AwardKillReward(killerSource, victimSource, inZone)
  print(('[KOTH] AwardKillReward called - Killer: %s (type: %s) | Victim: %s (type: %s) | In Zone: %s'):format(
    tostring(killerSource), type(killerSource), tostring(victimSource), type(victimSource), tostring(inZone)))

  -- Convert to number if needed (FiveM sometimes passes strings)
  local killerSourceNum = tonumber(killerSource)
  local victimSourceNum = tonumber(victimSource)

  print(('[KOTH] Converted IDs - Killer: %s | Victim: %s'):format(tostring(killerSourceNum), tostring(victimSourceNum)))

  if not playerData[killerSourceNum] then
    print(('[KOTH] No player data found for killer %s'):format(killerSourceNum))
    return
  end

  if not playerData[victimSourceNum] then
    print(('[KOTH] No player data found for victim %s'):format(victimSourceNum))
    return
  end

  print(('[KOTH] Both players found, processing reward...'):format())

  local baseXP = 50
  local baseMoney = 50

  local xpReward = inZone and (baseXP * 2) or baseXP
  local moneyReward = inZone and (baseMoney * 2) or baseMoney

  -- Update killer stats (use converted numbers)
  local killerData = playerData[killerSourceNum]
  local oldLevel = killerData.level

  killerData.money = killerData.money + moneyReward
  killerData.xp = killerData.xp + xpReward
  killerData.kills = killerData.kills + 1

  if inZone then
    killerData.zone_kills = killerData.zone_kills + 1
  end

  -- Calculate new level
  killerData.level = CalculateLevel(killerData.xp)

  -- Update victim stats (use converted numbers)
  local victimData = playerData[victimSourceNum]
  victimData.deaths = victimData.deaths + 1

  -- Send reward notification to killer (use converted numbers)
  TriggerClientEvent('koth:showKillReward', killerSourceNum, {
    xp = xpReward,
    money = moneyReward,
    inZone = inZone,
    victimName = GetPlayerName(victimSourceNum)
  })

  -- Check for level up
  if killerData.level > oldLevel then
    TriggerClientEvent('koth:levelUp', killerSourceNum, {
      newLevel = killerData.level,
      oldLevel = oldLevel
    })
  end

  -- Update client data (use converted numbers)
  TriggerClientEvent('koth:updatePlayerData', killerSourceNum, killerData)
  TriggerClientEvent('koth:updatePlayerData', victimSourceNum, victimData)

  -- Save data (use converted numbers)
  SavePlayerData(killerSourceNum)
  SavePlayerData(victimSourceNum)

  print(('[KOTH] Kill reward: %s killed %s | XP: +%d | Money: +$%d | Zone: %s'):format(
    GetPlayerName(killerSourceNum),
    GetPlayerName(victimSourceNum),
    xpReward,
    moneyReward,
    inZone and 'YES' or 'NO'
  ))
end

-- Kill detection event
RegisterNetEvent('koth:playerKilled', function(killerSource, victimSource, inZone)
  print(('[KOTH] Kill event received - Killer: %s | Victim: %s | In Zone: %s'):format(
    tostring(killerSource), tostring(victimSource), tostring(inZone)))

  if killerSource and victimSource and killerSource ~= victimSource then
    print(('[KOTH] Processing kill reward for killer %s'):format(killerSource))
    AwardKillReward(killerSource, victimSource, inZone)
  else
    print('[KOTH] Kill event ignored - invalid data or self-kill')
  end
end)

-- Debug command to check server player data
RegisterCommand('serverstats', function(source)
  if source == 0 then
    print('[KOTH] Server player data:')
    for playerId, data in pairs(playerData) do
      print(('  Player %d: Money=$%d, Level=%d, XP=%d'):format(
        playerId, data.money or 0, data.level or 0, data.xp or 0))
    end
  else
    if playerData[source] then
      print(('[KOTH] Player %d data - Money: $%d, Level: %d, XP: %d'):format(
        source, playerData[source].money or 0, playerData[source].level or 0, playerData[source].xp or 0))
    else
      print(('[KOTH] No data found for player %d'):format(source))
    end
  end
end, true)

-- Debug commands for testing
RegisterCommand('loaddata', function(source)
  print(('[KOTH] Manually loading data for %s'):format(GetPlayerName(source)))
  LoadPlayerData(source)
end, false)

RegisterCommand('checkdata', function(source)
  print(('[KOTH] Player data for %s: %s'):format(GetPlayerName(source), json.encode(playerData[source] or 'nil')))
  print(('[KOTH] playerData table type: %s'):format(type(playerData)))
  for k, v in pairs(playerData) do
    print(('[KOTH] playerData[%s] = %s'):format(k, json.encode(v)))
  end
end, false)

-- ADMIN PANEL INTEGRATION - Export functions for admin panel to use
exports('GivePlayerMoney', function(playerId, amount)
  local source = tonumber(playerId)
  if not source or not playerData[source] then
    print(('[KOTH] GivePlayerMoney: Invalid player ID %s or no cached data'):format(tostring(playerId)))
    return false
  end
  
  -- Update cached data
  playerData[source].money = (playerData[source].money or 0) + amount
  
  -- Save to database
  SavePlayerData(source)
  
  -- Update client HUD immediately
  TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
  
  print(('[KOTH] Admin gave $%d to player %d - new balance: $%d'):format(amount, source, playerData[source].money))
  return true
end)

exports('GivePlayerXP', function(playerId, amount)
  local source = tonumber(playerId)
  if not source or not playerData[source] then
    print(('[KOTH] GivePlayerXP: Invalid player ID %s or no cached data'):format(tostring(playerId)))
    return false
  end
  
  -- Update cached data
  local oldLevel = playerData[source].level
  playerData[source].xp = (playerData[source].xp or 0) + amount
  playerData[source].level = CalculateLevel(playerData[source].xp)
  
  -- Save to database
  SavePlayerData(source)
  
  -- Check for level up
  if playerData[source].level > oldLevel then
    TriggerClientEvent('koth:levelUp', source, {
      newLevel = playerData[source].level,
      oldLevel = oldLevel
    })
  end
  
  -- Update client HUD immediately
  TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
  
  print(('[KOTH] Admin gave %d XP to player %d - new XP: %d, level: %d'):format(amount, source, playerData[source].xp, playerData[source].level))
  return true
end)

exports('SetPlayerLevel', function(playerId, level)
  local source = tonumber(playerId)
  if not source or not playerData[source] then
    print(('[KOTH] SetPlayerLevel: Invalid player ID %s or no cached data'):format(tostring(playerId)))
    return false
  end
  
  -- Calculate XP for the new level
  local levels = {
    {level = 1, required = 0},
    {level = 2, required = 100},
    {level = 3, required = 250},
    {level = 4, required = 500},
    {level = 5, required = 1000},
    {level = 6, required = 1750},
    {level = 7, required = 2750},
    {level = 8, required = 4000},
    {level = 9, required = 6000},
    {level = 10, required = 8500}
  }
  
  local requiredXP = 0
  for _, levelData in ipairs(levels) do
    if levelData.level == level then
      requiredXP = levelData.required
      break
    end
  end
  
  -- Update cached data
  local oldLevel = playerData[source].level
  playerData[source].level = level
  playerData[source].xp = requiredXP
  
  -- Save to database
  SavePlayerData(source)
  
  -- Check for level up notification
  if level > oldLevel then
    TriggerClientEvent('koth:levelUp', source, {
      newLevel = level,
      oldLevel = oldLevel
    })
  end
  
  -- Update client HUD immediately
  TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
  
  print(('[KOTH] Admin set player %d to level %d (XP: %d)'):format(source, level, requiredXP))
  return true
end)

exports('ResetPlayerStats', function(playerId)
  local source = tonumber(playerId)
  if not source or not playerData[source] then
    print(('[KOTH] ResetPlayerStats: Invalid player ID %s or no cached data'):format(tostring(playerId)))
    return false
  end
  
  -- Reset cached data to defaults
  playerData[source].money = 1000
  playerData[source].xp = 0
  playerData[source].level = 1
  playerData[source].kills = 0
  playerData[source].deaths = 0
  playerData[source].zone_kills = 0
  
  -- Save to database
  SavePlayerData(source)
  
  -- Update client HUD immediately
  TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
  
  print(('[KOTH] Admin reset stats for player %d'):format(source))
  return true
end)

-- Event to handle admin panel money/XP updates
RegisterNetEvent('koth:adminUpdatePlayerData', function(playerId, newData)
  local source = tonumber(playerId)
  if not source or not playerData[source] then
    print(('[KOTH] adminUpdatePlayerData: Invalid player ID %s or no cached data'):format(tostring(playerId)))
    return
  end
  
  -- Update cached data with new values
  if newData.money then
    playerData[source].money = newData.money
  end
  if newData.xp then
    local oldLevel = playerData[source].level
    playerData[source].xp = newData.xp
    playerData[source].level = newData.level or CalculateLevel(newData.xp)
    
    -- Check for level up
    if playerData[source].level > oldLevel then
      TriggerClientEvent('koth:levelUp', source, {
        newLevel = playerData[source].level,
        oldLevel = oldLevel
      })
    end
  end
  if newData.level then
    playerData[source].level = newData.level
  end
  
  -- Save to database
  SavePlayerData(source)
  
  -- Update client HUD immediately
  TriggerClientEvent('koth:updatePlayerData', source, playerData[source])
  
  print(('[KOTH] Admin updated player %d data via event'):format(source))
end)

print('[KOTH] Server loaded successfully')
