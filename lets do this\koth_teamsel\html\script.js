console.log('[KOTH] Script loading...');

window.addEventListener('message', function(ev) {
  console.log('[KOTH] Received message:', ev.data);

  try {
    if (!ev.data || !ev.data.action) return;

    // Handle permanent HUD events first (don't need overlay)
    if (ev.data.action === 'updateHealth') {
      updatePlayerHealth(ev.data.health);
      return;
    } else if (ev.data.action === 'updateMoney') {
      updatePlayerMoney(ev.data.money);
      return;
    } else if (ev.data.action === 'updateTeamCounts') {
      updateTeamCountsData(ev.data.teamCounts);
      return;
    } else if (ev.data.action === 'updateZonePoints') {
      updateZonePointsData(ev.data.zonePoints);
      return;
    } else if (ev.data.action === 'showKothZone') {
      console.log('[KOTH] Show zone status');
      showKothZoneStatus();
      if (ev.data.status) {
        updateKothStatus(ev.data.status);
      }
      return;
    } else if (ev.data.action === 'hideKothZone') {
      console.log('[KOTH] Hide zone status');
      hideKothZoneStatus();
      return;
    } else if (ev.data.action === 'updateKothStatus') {
      console.log('[KOTH] KOTH status update received:', ev.data.status);
      updateKothStatus(ev.data.status);
      return;
    } else if (ev.data.action === 'initHUD') {
      console.log('[KOTH] Initialize HUD with data:', ev.data);
      if (ev.data.playerData) {
        currentGameData = { ...currentGameData, ...ev.data.playerData };
      }
      initializeGameHUD();
      return;
    } else if (ev.data.action === 'showDeathScreen') {
      console.log('[KOTH] Show death screen:', ev.data.killer);
      showDeathScreen(ev.data.killer);
      return;
    } else if (ev.data.action === 'hideDeathScreen') {
      console.log('[KOTH] Hide death screen');
      hideDeathScreen();
      return;
    } else if (ev.data.action === 'showKillReward') {
      console.log('[KOTH] Show kill reward:', ev.data);
      showKillReward(ev.data);
      return;
    } else if (ev.data.action === 'showLevelUp') {
      console.log('[KOTH] Show level up:', ev.data);
      showLevelUp(ev.data);
      return;
    } else if (ev.data.action === 'updatePlayerData') {
      console.log('[KOTH] Update player data received:', ev.data);
      console.log('[KOTH] Data object:', ev.data.data);
      updatePlayerData(ev.data.data);
      return;
    }

    const overlay = document.getElementById('overlay');
    const teamSelect = document.getElementById('team-select');
    const menuContainer = document.getElementById('menu-container');
    const menuTitle = document.getElementById('menu-title');
    const itemsContainer = document.getElementById('items');

    if (!overlay) {
      console.error('[KOTH] Missing overlay element');
      return;
    }

    if (ev.data.action === 'showTeamSelect') {
      console.log('[KOTH] Showing team select');

      if (teamSelect) {
        // Update counts if provided
        if (ev.data.counts) {
          for (let team in ev.data.counts) {
            const countEl = document.getElementById('count-' + team);
            if (countEl) {
              countEl.textContent = ev.data.counts[team];
            }
          }
          
          // Hide loading indicator once data is loaded
          const loadingIndicator = document.querySelector('.loading-indicator');
          if (loadingIndicator) {
            loadingIndicator.classList.add('hidden');
          }
        }

        overlay.style.display = 'flex';
        teamSelect.style.display = 'flex';
        if (menuContainer) menuContainer.style.display = 'none';
      }

    } else if (ev.data.action === 'showMenu') {
      console.log('[KOTH] Showing menu:', ev.data.type);

      if (menuContainer && menuTitle && itemsContainer) {
        if (teamSelect) teamSelect.style.display = 'none';

        menuTitle.textContent = ev.data.type === 'vehicles' ? 'Vehicles' : 'Classes';
        itemsContainer.innerHTML = '';

        if (ev.data.type === 'vehicles' && ev.data.items) {
          // Show vehicles shop instead of old menu
          const vehiclesShop = document.getElementById('vehicles-shop');
          const vehiclesGrid = document.getElementById('vehicles-grid');
          const vehiclePlayerMoney = document.getElementById('vehicle-player-money');

          if (vehiclesShop && vehiclesGrid) {
            // Hide other UIs
            if (teamSelect) teamSelect.style.display = 'none';
            if (menuContainer) menuContainer.style.display = 'none';

            // Update player money - handle both number and undefined cases
            console.log('[KOTH] Vehicle shop money data:', ev.data.money);
            const moneyValue = typeof ev.data.money === 'number' ? ev.data.money : 0;
            if (vehiclePlayerMoney) {
              vehiclePlayerMoney.textContent = moneyValue.toLocaleString();
              console.log('[KOTH] Updated vehicle shop money display to:', moneyValue);
              
              // Flash money display to show it updated
              vehiclePlayerMoney.style.color = '#00ff00';
              setTimeout(() => {
                vehiclePlayerMoney.style.color = '';
              }, 500);
            }

            // Clear and populate vehicles grid
            vehiclesGrid.innerHTML = '';

            ev.data.items.forEach(function(v) {
              const card = document.createElement('div');
              card.className = 'vehicle-card';

              // Check affordability
              const playerMoneyAmount = moneyValue;
              const canAffordBuy = playerMoneyAmount >= v.cost;
              const canAffordRent = playerMoneyAmount >= v.rent;
              console.log(`[KOTH] Vehicle ${v.name}: buy=$${v.cost}, rent=$${v.rent}, playerMoney=$${playerMoneyAmount}, canAffordBuy=${canAffordBuy}, canAffordRent=${canAffordRent}`);

              if (!canAffordBuy && !canAffordRent) {
                card.className += ' unaffordable';
              }

              card.innerHTML =
                '<img src="' + v.img + '" alt="' + v.name + '" class="vehicle-image">' +
                '<div class="vehicle-name">' + v.name + '</div>' +
                '<div class="vehicle-price">$' + v.cost.toLocaleString() + '</div>' +
                '<div class="vehicle-rent-price">Rent: $' + v.rent.toLocaleString() + '</div>' +
                '<div class="vehicle-buttons">' +
                  '<button class="vehicle-buy-btn' + (canAffordBuy ? '' : ' disabled') + '" data-name="' + v.name + '" data-price="' + v.cost + '" data-type="buy" ' + (canAffordBuy ? '' : 'disabled') + '>BUY</button>' +
                  '<button class="vehicle-rent-btn' + (canAffordRent ? '' : ' disabled') + '" data-name="' + v.name + '" data-price="' + v.rent + '" data-type="rent" ' + (canAffordRent ? '' : 'disabled') + '>RENT</button>' +
                '</div>';
              vehiclesGrid.appendChild(card);
            });

            overlay.style.display = 'flex';
            vehiclesShop.style.display = 'block';
            return; // Skip the old menu logic
          }
        } else if (ev.data.type === 'classes' && ev.data.items) {
          // Show classes selection instead of old menu
          const classesSelection = document.getElementById('classes-selection');
          const classesContainer = document.getElementById('classes-container');

          if (classesSelection && classesContainer) {
            // Hide other UIs
            if (teamSelect) teamSelect.style.display = 'none';
            if (menuContainer) menuContainer.style.display = 'none';

            // Clear and populate classes container
            classesContainer.innerHTML = '';
            
            // Store player money for later use in weapon shop
            const playerMoney = typeof ev.data.money === 'number' ? ev.data.money : 0;
            window.lastKnownPlayerMoney = playerMoney;

            ev.data.items.forEach(function(c) {
              const card = document.createElement('div');
              card.className = 'class-card';
              if (c.id === 'medic') card.className += ' medic';
              if (c.locked) card.className += ' locked';

              const lockIcon = c.locked ? '<div class="class-lock-icon">🔒</div>' : '';

              card.innerHTML =
                '<img src="' + c.img + '" alt="' + (c.name || c.label) + '" class="class-image">' +
                '<div class="class-info">' +
                  '<div class="class-name' + (c.id === 'medic' ? ' medic' : '') + '">' + (c.name || c.label) + '</div>' +
                  '<div class="class-unlock">' + (c.unlock || 'Unlock at level 1') + '</div>' +
                  lockIcon +
                '</div>';

              card.setAttribute('data-id', c.id);
              card.setAttribute('data-type', 'class');
              card.setAttribute('data-locked', c.locked ? 'true' : 'false');
              classesContainer.appendChild(card);
            });

            overlay.style.display = 'flex';
            classesSelection.style.display = 'block';
            return; // Skip the old menu logic
          }
        }

        overlay.style.display = 'flex';
        menuContainer.style.display = 'flex';
      }

    } else if (ev.data.action === 'showWeaponSelect') {
      console.log('[KOTH] Showing weapon selection for class:', ev.data.class);

      const weaponsShop = document.getElementById('weapons-shop');
      const weaponsGrid = document.getElementById('weapons-grid');
      const playerMoney = document.getElementById('player-money');
      const classesSelection = document.getElementById('classes-selection');

      if (weaponsShop && weaponsGrid) {
        // Hide other UIs including classes selection
        if (teamSelect) {
          teamSelect.style.display = 'none';
          console.log('[KOTH] Hidden team select');
        }
        if (menuContainer) {
          menuContainer.style.display = 'none';
          console.log('[KOTH] Hidden menu container');
        }
        if (classesSelection) {
          classesSelection.style.display = 'none';
          console.log('[KOTH] Hidden classes selection');
        }

        // Update player money - handle both number and undefined cases
        console.log('[KOTH] Weapon shop money data:', ev.data.money);
        const moneyValue = typeof ev.data.money === 'number' ? ev.data.money : (window.lastKnownPlayerMoney || 0);
        if (playerMoney) {
          playerMoney.textContent = moneyValue.toLocaleString();
          console.log('[KOTH] Updated weapon shop money display to:', moneyValue);
          
          // Flash money display to show it updated
          playerMoney.style.color = '#00ff00';
          setTimeout(() => {
            playerMoney.style.color = '';
          }, 500);
        }

        // Clear and populate weapons grid
        weaponsGrid.innerHTML = '';

        if (ev.data.weapons) {
          ev.data.weapons.forEach(function(w) {
            const card = document.createElement('div');
            card.className = 'weapon-card';

            // Check if player can afford this weapon
            const playerMoneyAmount = moneyValue;
            const canAfford = playerMoneyAmount >= w.price;
            console.log(`[KOTH] Weapon ${w.name}: price=$${w.price}, playerMoney=$${playerMoneyAmount}, canAfford=${canAfford}`);
            if (!canAfford) {
              card.className += ' unaffordable';
            }

            card.innerHTML =
              '<img src="' + w.img + '" alt="' + w.name + '" class="weapon-image">' +
              '<div class="weapon-name">' + w.name + '</div>' +
              '<div class="weapon-price">$' + w.price.toLocaleString() + '</div>' +
              '<button class="weapon-buy-btn' + (canAfford ? '' : ' disabled') + '" data-weapon="' + w.weapon + '" data-class="' + ev.data.class + '" data-price="' + w.price + '" data-type="weapon" ' + (canAfford ? '' : 'disabled') + '>BUY</button>';
            weaponsGrid.appendChild(card);
          });
        }

        overlay.style.display = 'flex';
        weaponsShop.style.display = 'block';
      }

    } else if (ev.data.action === 'hideAll') {
      console.log('[KOTH] Hiding all');
      overlay.style.display = 'none';
      if (teamSelect) teamSelect.style.display = 'none';
      if (menuContainer) menuContainer.style.display = 'none';

      const weaponsShop = document.getElementById('weapons-shop');
      if (weaponsShop) weaponsShop.style.display = 'none';

      const vehiclesShop = document.getElementById('vehicles-shop');
      if (vehiclesShop) vehiclesShop.style.display = 'none';

      const classesSelection = document.getElementById('classes-selection');
      if (classesSelection) classesSelection.style.display = 'none';
    }

  } catch (error) {
    console.error('[KOTH] Error:', error);
  }
});

// Click handler for all buttons
document.addEventListener('click', function(e) {
  try {
    // Team card clicks (new enhanced UI)
    if (e.target.classList.contains('team-card') || e.target.closest('.team-card')) {
      const teamCard = e.target.classList.contains('team-card') ? e.target : e.target.closest('.team-card');
      const team = teamCard.getAttribute('data-team');
      console.log('[KOTH] Team card clicked:', team);

      if (team) {
        // Add visual feedback
        teamCard.style.transform = 'translateY(-5px) scale(0.95)';
        teamCard.style.opacity = '0.8';
        
        // Hide loading indicator
        const loadingIndicator = document.querySelector('.loading-indicator');
        if (loadingIndicator) {
          loadingIndicator.style.opacity = '0';
        }

        fetch('https://' + GetParentResourceName() + '/selectTeam', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ team: team })
        }).catch(function(err) {
          console.error('[KOTH] Team select error:', err);
          // Reset visual feedback on error
          teamCard.style.transform = '';
          teamCard.style.opacity = '';
          if (loadingIndicator) {
            loadingIndicator.style.opacity = '1';
          }
        });
      }
    }

    // Legacy team button clicks (fallback)
    else if (e.target.classList.contains('team-btn')) {
      const team = e.target.getAttribute('data-team');
      console.log('[KOTH] Team clicked:', team);

      if (team) {
        fetch('https://' + GetParentResourceName() + '/selectTeam', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ team: team })
        }).catch(function(err) {
          console.error('[KOTH] Team select error:', err);
        });
      }
    }

    // Action button clicks (old menu system)
    else if (e.target.classList.contains('action-btn')) {
      const type = e.target.getAttribute('data-type');
      const name = e.target.getAttribute('data-name');
      const id = e.target.getAttribute('data-id');
      const weapon = e.target.getAttribute('data-weapon');
      const classType = e.target.getAttribute('data-class');
      const price = e.target.getAttribute('data-price');

      if (type === 'buy' && name) {
        console.log('[KOTH] Buy clicked:', name, 'price:', price, 'button disabled:', e.target.disabled);
        if (e.target.disabled) {
          console.log('[KOTH] Button is disabled, not processing purchase');
          return;
        }
        fetch('https://' + GetParentResourceName() + '/buyVehicle', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ name: name, price: price })
        });
      } else if (type === 'rent' && name) {
        console.log('[KOTH] Rent clicked:', name, 'price:', price, 'button disabled:', e.target.disabled);
        if (e.target.disabled) {
          console.log('[KOTH] Button is disabled, not processing rental');
          return;
        }
        fetch('https://' + GetParentResourceName() + '/rentVehicle', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ name: name, price: price })
        });
      } else if (type === 'weapon' && weapon && classType) {
        console.log('[KOTH] Weapon selected:', weapon, 'for class:', classType);
        fetch('https://' + GetParentResourceName() + '/selectWeapon', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ weapon: weapon, class: classType, price: price })
        });
      } else if (id) {
        console.log('[KOTH] Class selected:', id);
        fetch('https://' + GetParentResourceName() + '/selectClass', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ id: id })
        });
      }
    }

    // Vehicle shop button clicks
    else if (e.target.classList.contains('vehicle-buy-btn') || e.target.classList.contains('vehicle-rent-btn')) {
      const type = e.target.classList.contains('vehicle-buy-btn') ? 'buy' : 'rent';
      const name = e.target.getAttribute('data-name');
      const price = e.target.getAttribute('data-price');

      if (type === 'buy' && name) {
        console.log('[KOTH] Vehicle buy clicked:', name, 'price:', price);
        fetch('https://' + GetParentResourceName() + '/buyVehicle', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ name: name, price: price })
        });
      } else if (type === 'rent' && name) {
        console.log('[KOTH] Vehicle rent clicked:', name, 'price:', price);
        fetch('https://' + GetParentResourceName() + '/rentVehicle', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ name: name, price: price })
        });
      }
    }

    // Weapon shop button clicks
    else if (e.target.classList.contains('weapon-buy-btn')) {
      const weapon = e.target.getAttribute('data-weapon');
      const classType = e.target.getAttribute('data-class');
      const price = e.target.getAttribute('data-price');

      console.log('[KOTH] Weapon buy clicked:', weapon, 'class:', classType, 'price:', price);
      fetch('https://' + GetParentResourceName() + '/selectWeapon', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ weapon: weapon, class: classType, price: price })
      });
    }

    // Class card clicks
    else if (e.target.classList.contains('class-card') || e.target.closest('.class-card')) {
      const card = e.target.classList.contains('class-card') ? e.target : e.target.closest('.class-card');
      const classId = card.getAttribute('data-id');
      const isLocked = card.getAttribute('data-locked') === 'true';

      if (classId) {
        if (isLocked) {
          console.log('[KOTH] Class is locked:', classId);
          // Could add visual feedback here (shake animation, etc.)
          return;
        }

        console.log('[KOTH] Class selected:', classId);

        // Immediately hide classes UI
        const classesSelection = document.getElementById('classes-selection');
        if (classesSelection) {
          console.log('[KOTH] Hiding classes selection UI');
          classesSelection.style.display = 'none';
        }

        fetch('https://' + GetParentResourceName() + '/selectClass', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ id: classId })
        });
      }
    }

    // Close button
    else if (e.target.id === 'close-btn' || e.target.id === 'shop-close' || e.target.id === 'vehicle-shop-close' || e.target.id === 'classes-close') {
      console.log('[KOTH] Close clicked');
      fetch('https://' + GetParentResourceName() + '/closeMenu', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      });
    }

  } catch (error) {
    console.error('[KOTH] Click error:', error);
  }
});

console.log('[KOTH] Script loaded');

// PERMANENT GAME HUD SYSTEM
let currentGameData = {
  playerName: 'andyflip9',
  playerMoney: 49235,
  playerLevel: 41,
  playerXP: 2847,
  playerMaxXP: 4500,
  playerHealth: 100,
  teamCounts: { red: 0, green: 0, blue: 0 },
  zonePoints: { red: 0, green: 0, blue: 0 },
  kothStatus: {
    active: false,
    controllingTeam: null,
    captureProgress: 0,
    dominantTeam: null,
    isContested: false
  }
};

// Initialize permanent HUD
function initializeGameHUD() {
  console.log('[KOTH] Initializing permanent game HUD');
  updatePlayerInfo();
  updateTeamCounts();
  updateZonePoints();
  updateHealthBar();
  updateKothZoneStatus();
}

// Update player information
function updatePlayerInfo() {
  const playerName = document.getElementById('player-name');
  const playerMoney = document.getElementById('player-money-display');
  const playerLevel = document.getElementById('player-level');
  const playerXP = document.getElementById('player-xp');

  if (playerName) playerName.textContent = currentGameData.playerName;
  if (playerMoney) playerMoney.textContent = '$' + currentGameData.playerMoney.toLocaleString();
  if (playerLevel) playerLevel.textContent = currentGameData.playerLevel;
  if (playerXP) playerXP.textContent = currentGameData.playerXP + '/' + currentGameData.playerMaxXP;
}

// Update team player counts (person icons section)
function updateTeamCounts() {
  const redPlayers = document.getElementById('red-players');
  const greenPlayers = document.getElementById('green-players');
  const bluePlayers = document.getElementById('blue-players');

  if (redPlayers) redPlayers.textContent = currentGameData.teamCounts.red || 0;
  if (greenPlayers) greenPlayers.textContent = currentGameData.teamCounts.green || 0;
  if (bluePlayers) bluePlayers.textContent = currentGameData.teamCounts.blue || 0;
}

// Update zone control points (colored boxes section)
function updateZonePoints() {
  const redPoints = document.getElementById('red-zone-points');
  const greenPoints = document.getElementById('green-zone-points');
  const bluePoints = document.getElementById('blue-zone-points');

  if (redPoints) redPoints.textContent = currentGameData.zonePoints.red || 0;
  if (greenPoints) greenPoints.textContent = currentGameData.zonePoints.green || 0;
  if (bluePoints) bluePoints.textContent = currentGameData.zonePoints.blue || 0;
}


// Update health bar
function updateHealthBar() {
  const healthFill = document.getElementById('health-fill');
  const healthText = document.getElementById('health-text');

  if (healthFill) {
    const healthPercent = Math.max(0, Math.min(100, currentGameData.playerHealth));
    healthFill.style.width = healthPercent + '%';
  }

  if (healthText) {
    healthText.textContent = Math.round(currentGameData.playerHealth);
  }
}

// Show/Hide KOTH zone status
function showKothZoneStatus() {
  const kothZoneStatus = document.getElementById('koth-zone-status');
  if (kothZoneStatus) {
    kothZoneStatus.style.display = 'block';
    currentGameData.kothStatus.active = true;
    console.log('[KOTH] Zone status shown');
  }
}

function hideKothZoneStatus() {
  const kothZoneStatus = document.getElementById('koth-zone-status');
  if (kothZoneStatus) {
    kothZoneStatus.style.display = 'none';
    currentGameData.kothStatus.active = false;
    console.log('[KOTH] Zone status hidden');
  }
}

// Update KOTH zone status
function updateKothZoneStatus() {
  if (!currentGameData.kothStatus.active) return;

  const status = currentGameData.kothStatus;

  // Update progress bar
  const progressFill = document.getElementById('koth-progress-fill');
  const progressText = document.getElementById('koth-progress-text');

  if (progressFill && progressText) {
    const percentage = Math.round((status.captureProgress / 100) * 100);
    progressFill.style.width = percentage + '%';

    // Update progress bar color based on dominant team
    progressFill.className = 'koth-progress-fill';
    if (status.dominantTeam) {
      progressFill.classList.add(status.dominantTeam);
    }

    // Update progress text
    let statusText = '';
    if (status.controllingTeam) {
      statusText = status.controllingTeam.toUpperCase() + ' CONTROLLED';
    } else if (status.isContested) {
      statusText = 'CONTESTED';
    } else if (status.dominantTeam) {
      statusText = status.dominantTeam.toUpperCase() + ' CAPTURING';
    } else {
      statusText = 'NEUTRAL';
    }

    progressText.textContent = statusText;
  }
}

// Handle health updates from game
function updatePlayerHealth(health) {
  currentGameData.playerHealth = health;
  updateHealthBar();
}

// Handle money updates from game
function updatePlayerMoney(money) {
  currentGameData.playerMoney = money;
  updatePlayerInfo();
}

// Handle team count updates (for person icons)
function updateTeamCountsData(teamCounts) {
  currentGameData.teamCounts = teamCounts;
  updateTeamCounts();
}

// Handle zone points updates (for colored boxes)
function updateZonePointsData(zonePoints) {
  currentGameData.zonePoints = zonePoints;
  updateZonePoints();
}


// Handle KOTH status updates
function updateKothStatus(status) {
  currentGameData.kothStatus = {
    ...currentGameData.kothStatus,
    ...status
  };
  updateKothZoneStatus();
}

// DEATH SCREEN SYSTEM
let deathScreenActive = false;
let respawnHoldProgress = 0;
let respawnHoldActive = false;
let bleedoutTimer = 50;
let bleedoutInterval = null;
let respawnInterval = null;

// Show death screen
function showDeathScreen(killerData) {
  const deathScreen = document.getElementById('death-screen');
  const killerIdElement = document.getElementById('killer-id');
  const killerNameElement = document.getElementById('killer-name');
  const bleedoutTimerElement = document.getElementById('bleedout-timer');
  const respawnFill = document.getElementById('respawn-fill');

  if (deathScreen) {
    // Set killer information
    if (killerData) {
      if (killerIdElement) killerIdElement.textContent = killerData.id || '000000';
      if (killerNameElement) killerNameElement.textContent = killerData.name || 'Unknown';
    }

    // Reset values
    bleedoutTimer = 50;
    respawnHoldProgress = 0;
    respawnHoldActive = false;

    if (bleedoutTimerElement) bleedoutTimerElement.textContent = bleedoutTimer;
    if (respawnFill) respawnFill.style.width = '0%';

    // Show death screen
    deathScreen.style.display = 'flex';
    deathScreenActive = true;

    // Start bleedout timer
    startBleedoutTimer();

    console.log('[KOTH] Death screen shown');
  }
}

// Hide death screen
function hideDeathScreen() {
  const deathScreen = document.getElementById('death-screen');
  if (deathScreen) {
    deathScreen.style.display = 'none';
    deathScreenActive = false;

    // Clear timers
    if (bleedoutInterval) {
      clearInterval(bleedoutInterval);
      bleedoutInterval = null;
    }
    if (respawnInterval) {
      clearInterval(respawnInterval);
      respawnInterval = null;
    }

    console.log('[KOTH] Death screen hidden');
  }
}

// Start bleedout countdown
function startBleedoutTimer() {
  if (bleedoutInterval) clearInterval(bleedoutInterval);

  bleedoutInterval = setInterval(function() {
    bleedoutTimer--;
    const bleedoutTimerElement = document.getElementById('bleedout-timer');
    if (bleedoutTimerElement) {
      bleedoutTimerElement.textContent = bleedoutTimer;
    }

    // Auto respawn when timer reaches 0
    if (bleedoutTimer <= 0) {
      console.log('[KOTH] Bleedout timer expired, auto respawning');
      clearInterval(bleedoutInterval);

      // Trigger respawn
      fetch('https://' + GetParentResourceName() + '/playerRespawn', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'bleedout' })
      });
    }
  }, 1000);
}

// Handle respawn hold progress
function updateRespawnProgress() {
  const respawnFill = document.getElementById('respawn-fill');
  if (respawnFill) {
    const percentage = (respawnHoldProgress / 5.0) * 100; // 5 seconds to respawn
    respawnFill.style.width = percentage + '%';

    // Complete respawn when progress reaches 100%
    if (respawnHoldProgress >= 5.0) {
      console.log('[KOTH] Respawn hold complete');

      // Clear respawn interval
      if (respawnInterval) {
        clearInterval(respawnInterval);
        respawnInterval = null;
      }

      // Trigger respawn
      fetch('https://' + GetParentResourceName() + '/playerRespawn', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'manual' })
      });
    }
  }
}

// Handle E key for respawn (fixed version)
document.addEventListener('keydown', function(e) {
  if (!deathScreenActive) return;

  if (e.code === 'KeyE') { // Use e.code instead of e.key for better detection
    e.preventDefault(); // Prevent any default behavior

    if (!respawnHoldActive) {
      respawnHoldActive = true;
      respawnHoldProgress = 0;

      console.log('[KOTH] Started respawn hold');

      // Start respawn progress
      respawnInterval = setInterval(function() {
        respawnHoldProgress += 0.1; // Increment every 100ms (5 seconds total)
        updateRespawnProgress();
      }, 100);
    }
  }
});

document.addEventListener('keyup', function(e) {
  if (!deathScreenActive) return;

  if (e.code === 'KeyE') { // Use e.code instead of e.key for better detection
    e.preventDefault(); // Prevent any default behavior

    if (respawnHoldActive) {
      respawnHoldActive = false;
      respawnHoldProgress = 0;

      console.log('[KOTH] Stopped respawn hold');

      // Clear respawn interval
      if (respawnInterval) {
        clearInterval(respawnInterval);
        respawnInterval = null;
      }

      // Reset progress bar
      const respawnFill = document.getElementById('respawn-fill');
      if (respawnFill) {
        respawnFill.style.width = '0%';
      }
    }
  }
});

// Also handle the message event for key presses from the game
window.addEventListener('message', function(event) {
  const data = event.data;

  // Handle death screen specific events
  if (data.action === 'updateBleedoutTimer') {
    const timerElement = document.getElementById('bleedout-timer');
    if (timerElement) {
      timerElement.textContent = data.time;
      bleedoutTimer = data.time;
    }
    return;
  }
  
  if (data.action === 'updateRespawnProgress') {
    const progressElement = document.getElementById('respawn-fill');
    if (progressElement) {
      const progress = Math.min(data.progress * 100, 100);
      progressElement.style.width = progress + '%';
      respawnHoldProgress = data.progress * 5; // Convert back to seconds
    }
    return;
  }

  // Handle key events only when death screen is active
  if (!deathScreenActive) return;

  if (data.action === 'keyDown' && data.key === 'E') {
    if (!respawnHoldActive) {
      respawnHoldActive = true;
      respawnHoldProgress = 0;

      console.log('[KOTH] Started respawn hold (from game)');

      // Start respawn progress
      respawnInterval = setInterval(function() {
        respawnHoldProgress += 0.1; // Increment every 100ms (5 seconds total)
        updateRespawnProgress();
      }, 100);
    }
  } else if (data.action === 'keyUp' && data.key === 'E') {
    if (respawnHoldActive) {
      respawnHoldActive = false;
      respawnHoldProgress = 0;

      console.log('[KOTH] Stopped respawn hold (from game)');

      // Clear respawn interval
      if (respawnInterval) {
        clearInterval(respawnInterval);
        respawnInterval = null;
      }

      // Reset progress bar
      const respawnFill = document.getElementById('respawn-fill');
      if (respawnFill) {
        respawnFill.style.width = '0%';
      }
    }
  }
});

// KILL REWARD SYSTEM
function showKillReward(data) {
  const popup = document.getElementById('kill-reward-popup');
  const rewardValue = document.getElementById('kill-reward-value');
  const xpValue = document.getElementById('kill-xp-value');

  if (popup) {
    // Set reward data
    if (rewardValue) rewardValue.textContent = `$${data.money}`;
    if (xpValue) xpValue.textContent = data.xp;

    // Show popup
    popup.style.display = 'block';

    // Auto hide after 3 seconds
    setTimeout(function() {
      popup.style.display = 'none';
    }, 3000);

    console.log('[KOTH] Kill reward popup shown - Money: $' + data.money + ', XP: ' + data.xp);
  }
}

function showLevelUp(data) {
  const popup = document.getElementById('levelup-popup');
  const levelOld = document.getElementById('level-old');
  const levelNew = document.getElementById('level-new');

  if (popup) {
    // Set level data
    if (levelOld) levelOld.textContent = data.oldLevel || 1;
    if (levelNew) levelNew.textContent = data.newLevel || 2;

    // Show popup
    popup.style.display = 'block';

    // Auto hide after 4 seconds
    setTimeout(function() {
      popup.style.display = 'none';
    }, 4000);

    console.log('[KOTH] Level up popup shown - ' + (data.oldLevel || 1) + ' → ' + (data.newLevel || 2));
  }
}

function updatePlayerData(data) {
  // Store player data for future use
  if (data) {
    console.log('[KOTH] Player data updated:', data);

    // Update global game data
    if (data.player_name) currentGameData.playerName = data.player_name;
    if (typeof data.money === 'number') {
      currentGameData.playerMoney = data.money;
      // Store for shop use
      window.lastKnownPlayerMoney = data.money;
    }
    if (typeof data.level === 'number') currentGameData.playerLevel = data.level;
    if (typeof data.xp === 'number') currentGameData.playerXP = data.xp;
    if (typeof data.kills === 'number') currentGameData.playerKills = data.kills;

    // Update HUD elements using original IDs
    const playerNameElement = document.getElementById('player-name');
    const moneyElement = document.getElementById('player-money-display');
    const levelElement = document.getElementById('player-level');
    const killsElement = document.getElementById('player-kills');
    const xpTextElement = document.getElementById('xp-text');
    const xpFillElement = document.getElementById('xp-fill');

    if (playerNameElement && data.player_name) {
      playerNameElement.textContent = data.player_name;
      console.log('[KOTH] Updated player name to:', data.player_name);
    }

    if (moneyElement && typeof data.money === 'number') {
      const oldValue = moneyElement.textContent;
      moneyElement.textContent = `$${data.money.toLocaleString()}`;
      console.log('[KOTH] Updated money from', oldValue, 'to:', `$${data.money.toLocaleString()}`);

      // Visual feedback - flash the element
      moneyElement.style.color = '#7bed9f';
      setTimeout(() => {
        moneyElement.style.color = '';
      }, 1000);
    } else {
      console.log('[KOTH] Money update failed - element:', moneyElement, 'data.money:', data.money, 'type:', typeof data.money);
    }

    if (levelElement && typeof data.level === 'number') {
      levelElement.textContent = data.level;
      console.log('[KOTH] Updated level to:', data.level);
    }

    if (killsElement && typeof data.kills === 'number') {
      killsElement.textContent = data.kills;
      console.log('[KOTH] Updated kills to:', data.kills);
    }

    // Update XP progress bar and text - FIXED VERSION
    if (typeof data.xp === 'number' && typeof data.level === 'number') {
      // Level system matches server-side calculation
      const levels = [
        {level: 1, required: 0},
        {level: 2, required: 100},
        {level: 3, required: 250},
        {level: 4, required: 500},
        {level: 5, required: 1000},
        {level: 6, required: 1750},
        {level: 7, required: 2750},
        {level: 8, required: 4000},
        {level: 9, required: 6000},
        {level: 10, required: 8500},
        {level: 11, required: 12000},
        {level: 12, required: 16000},
        {level: 13, required: 21000},
        {level: 14, required: 27000},
        {level: 15, required: 35000},
        {level: 16, required: 45000},
        {level: 17, required: 57000},
        {level: 18, required: 72000},
        {level: 19, required: 90000},
        {level: 20, required: 112000}
      ];

      // Find current and next level requirements
      let currentLevelXP = 0;
      let nextLevelXP = 100; // Default for level 2

      for (let i = 0; i < levels.length; i++) {
        if (levels[i].level === data.level) {
          currentLevelXP = levels[i].required;
          if (i + 1 < levels.length) {
            nextLevelXP = levels[i + 1].required;
          } else {
            // Max level reached
            nextLevelXP = currentLevelXP;
          }
          break;
        }
      }

      const progressXP = data.xp - currentLevelXP;
      const neededXP = nextLevelXP - currentLevelXP;

      // Make sure progress doesn't exceed needed XP
      const displayProgressXP = Math.max(0, Math.min(progressXP, neededXP));
      const progressPercentage = neededXP > 0 ? (displayProgressXP / neededXP) * 100 : 100;

      // Update XP text - Find the element in the player stats section
      const xpStatElement = document.querySelector('#player-xp');
      if (xpStatElement) {
        xpStatElement.textContent = `${displayProgressXP}/${neededXP}`;
        console.log('[KOTH] Updated XP stat to:', `${displayProgressXP}/${neededXP}`);
      }

      // Update XP progress bar - Find the actual XP bar
      const xpBarElement = document.querySelector('.xp-fill');
      if (xpBarElement) {
        xpBarElement.style.width = `${progressPercentage}%`;
        console.log('[KOTH] Updated XP bar to:', `${progressPercentage}%`);
      }

      // Update XP text below the bar
      if (xpTextElement) {
        xpTextElement.textContent = `${displayProgressXP} / ${neededXP} XP`;
        console.log('[KOTH] Updated XP text to:', `${displayProgressXP} / ${neededXP} XP`);
      }

      console.log('[KOTH] XP Progress - Current:', displayProgressXP, 'Needed:', neededXP, 'Percentage:', progressPercentage);
    }

    console.log('[KOTH] HUD updated with new player data');
    
    // Also update any open shops with new money data
    updateOpenShops(data.money);
  }
}

// Function to update any open shops with new money data
function updateOpenShops(newMoney) {
  // Update vehicle shop if open
  const vehiclesShop = document.getElementById('vehicles-shop');
  if (vehiclesShop && vehiclesShop.style.display === 'block') {
    const vehiclePlayerMoney = document.getElementById('vehicle-player-money');
    if (vehiclePlayerMoney) {
      vehiclePlayerMoney.textContent = newMoney.toLocaleString();
      console.log('[KOTH] Updated vehicle shop money to:', newMoney);
    }
    
    // Update button states
    const vehicleButtons = document.querySelectorAll('.vehicle-buy-btn, .vehicle-rent-btn');
    vehicleButtons.forEach(btn => {
      const price = parseInt(btn.getAttribute('data-price'));
      const canAfford = newMoney >= price;
      btn.disabled = !canAfford;
      if (canAfford) {
        btn.classList.remove('disabled');
      } else {
        btn.classList.add('disabled');
      }
    });
  }
  
  // Update weapon shop if open
  const weaponsShop = document.getElementById('weapons-shop');
  if (weaponsShop && weaponsShop.style.display === 'block') {
    const playerMoney = document.getElementById('player-money');
    if (playerMoney) {
      playerMoney.textContent = newMoney.toLocaleString();
      console.log('[KOTH] Updated weapon shop money to:', newMoney);
    }
    
    // Update button states
    const weaponButtons = document.querySelectorAll('.weapon-buy-btn');
    weaponButtons.forEach(btn => {
      const price = parseInt(btn.getAttribute('data-price'));
      const canAfford = newMoney >= price;
      btn.disabled = !canAfford;
      if (canAfford) {
        btn.classList.remove('disabled');
      } else {
        btn.classList.add('disabled');
      }
    });
  }
}

// Test function to verify elements exist
function testHUDElements() {
  console.log('[KOTH] Testing HUD elements:');
  console.log('  player-name:', document.getElementById('player-name'));
  console.log('  player-money-display:', document.getElementById('player-money-display'));
  console.log('  player-level:', document.getElementById('player-level'));
  console.log('  player-xp:', document.getElementById('player-xp'));
}

// Test functions removed - they were interfering with real player data

// Initialize HUD when page loads
document.addEventListener('DOMContentLoaded', function() {
  console.log('[KOTH] DOM loaded, initializing HUD');
  initializeGameHUD();
  testHUDElements();
});

// WEAPON HOTBAR SYSTEM
let weaponHotbar = {
  slots: {
    1: null,
    2: null,
    3: null,
    4: null,
    5: null
  },
  activeSlot: 1
};

// Initialize weapon hotbar
function initializeWeaponHotbar() {
  console.log('[KOTH] Initializing weapon hotbar');
  
  // Show the hotbar
  const hotbar = document.getElementById('weapon-hotbar');
  if (hotbar) {
    hotbar.style.display = 'flex';
  }
  
  // Update all slots
  updateHotbarDisplay();
}

// Update hotbar display
function updateHotbarDisplay() {
  for (let slot = 1; slot <= 5; slot++) {
    updateHotbarSlot(slot);
  }
}

// Update individual hotbar slot
function updateHotbarSlot(slotNumber) {
  const slotElement = document.querySelector(`.hotbar-slot[data-slot="${slotNumber}"]`);
  if (!slotElement) return;
  
  const weaponData = weaponHotbar.slots[slotNumber];
  const weaponIcon = slotElement.querySelector('.weapon-icon');
  const ammoCount = slotElement.querySelector('.ammo-count');
  
  // Clear active class
  slotElement.classList.remove('active', 'empty');
  
  if (weaponData) {
    // Set weapon icon
    weaponIcon.className = 'weapon-icon ' + weaponData.iconClass;
    
    // Set ammo count
    if (ammoCount) {
      ammoCount.textContent = weaponData.ammo || '';
      ammoCount.style.display = weaponData.ammo ? 'block' : 'none';
    }
  } else {
    // Empty slot
    slotElement.classList.add('empty');
    weaponIcon.className = 'weapon-icon';
    if (ammoCount) {
      ammoCount.textContent = '';
      ammoCount.style.display = 'none';
    }
  }
  
  // Mark active slot
  if (slotNumber === weaponHotbar.activeSlot) {
    slotElement.classList.add('active');
  }
}

// Handle weapon slot selection
function selectWeaponSlot(slotNumber) {
  if (slotNumber < 1 || slotNumber > 5) return;
  
  weaponHotbar.activeSlot = slotNumber;
  updateHotbarDisplay();
  
  // Send to game
  fetch('https://' + GetParentResourceName() + '/selectWeaponSlot', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ slot: slotNumber })
  });
}

// Add weapon to hotbar
function addWeaponToHotbar(weaponData) {
  // Find first empty slot or use slot 1
  let targetSlot = null;
  for (let slot = 1; slot <= 5; slot++) {
    if (!weaponHotbar.slots[slot]) {
      targetSlot = slot;
      break;
    }
  }
  
  if (!targetSlot) {
    targetSlot = 1; // Replace first slot if all are full
  }
  
  weaponHotbar.slots[targetSlot] = weaponData;
  updateHotbarSlot(targetSlot);
}

// Handle hotbar updates from game
window.addEventListener('message', function(event) {
  const data = event.data;
  
  if (data.action === 'updateHotbar') {
    if (data.slots) {
      weaponHotbar.slots = data.slots;
      updateHotbarDisplay();
    }
  } else if (data.action === 'setActiveSlot') {
    weaponHotbar.activeSlot = data.slot;
    updateHotbarDisplay();
  } else if (data.action === 'addWeaponToHotbar') {
    addWeaponToHotbar(data.weapon);
  } else if (data.action === 'showHotbar') {
    initializeWeaponHotbar();
  } else if (data.action === 'hideHotbar') {
    const hotbar = document.getElementById('weapon-hotbar');
    if (hotbar) {
      hotbar.style.display = 'none';
    }
  }
});

// Handle keyboard input for slot selection
document.addEventListener('keydown', function(e) {
  // Check if it's a number key 1-5
  if (e.key >= '1' && e.key <= '5') {
    const slot = parseInt(e.key);
    selectWeaponSlot(slot);
  }
});

// Handle click on hotbar slots
document.addEventListener('click', function(e) {
  const slotElement = e.target.closest('.hotbar-slot');
  if (slotElement) {
    const slot = parseInt(slotElement.getAttribute('data-slot'));
    if (slot) {
      selectWeaponSlot(slot);
    }
  }
});
