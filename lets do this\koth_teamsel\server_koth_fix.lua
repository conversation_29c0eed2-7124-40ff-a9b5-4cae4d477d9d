-- KOTH ZONE FIX: Proper capture logic with 15-second point awards and round system

local kothZone = {
    controllingTeam = nil, -- Team that currently controls the zone
    playersInZone = {
        red = {},    -- Store player IDs
        blue = {},   -- Store player IDs  
        green = {}   -- Store player IDs
    },
    lastPointAward = 0 -- Track last time points were awarded
}

local zonePoints = { red = 0, blue = 0, green = 0 } -- Team points
local roundActive = true
local POINTS_TO_WIN = 150
local POINT_AWARD_INTERVAL = 15000 -- 15 seconds in milliseconds

-- Get count of players in zone for each team
local function getZoneCounts()
    local counts = {
        red = 0,
        blue = 0,
        green = 0
    }
    
    -- Count active players in each team's zone list
    for team, players in pairs(kothZone.playersInZone) do
        for playerId, _ in pairs(players) do
            -- Check if player is still connected
            if GetPlayerPing(playerId) > 0 then
                counts[team] = counts[team] + 1
            else
                -- Remove disconnected player
                kothZone.playersInZone[team][playerId] = nil
            end
        end
    end
    
    return counts
end

-- Determine which team controls the zone
local function determineControllingTeam()
    local counts = getZoneCounts()
    local maxCount = 0
    local controllingTeam = nil
    local tied = false
    
    -- Find team with most players
    for team, count in pairs(counts) do
        if count > maxCount then
            maxCount = count
            controllingTeam = team
            tied = false
        elseif count == maxCount and count > 0 then
            tied = true
        end
    end
    
    -- If tied or no players, no team controls
    if tied or maxCount == 0 then
        return nil
    end
    
    return controllingTeam
end

-- Override player entered zone event
RegisterNetEvent('koth:playerEnteredZone', function(team)
    local source = source
    if not team or not roundActive then return end
    
    -- Add player to their team's zone list
    if kothZone.playersInZone[team] then
        kothZone.playersInZone[team][source] = true
        print(('[KOTH ZONE FIX] Player %d entered zone for team %s'):format(source, team))
    end
    
    -- Update controlling team
    local newControllingTeam = determineControllingTeam()
    if newControllingTeam ~= kothZone.controllingTeam then
        kothZone.controllingTeam = newControllingTeam
        print(('[KOTH ZONE FIX] Zone control changed to: %s'):format(newControllingTeam or 'contested'))
        
        -- Notify all clients
        TriggerClientEvent('koth:zoneControlChanged', -1, newControllingTeam)
    end
    
    -- Send zone status update
    local counts = getZoneCounts()
    TriggerClientEvent('koth:updateZoneStatus', -1, {
        controllingTeam = kothZone.controllingTeam,
        playersInZone = counts
    })
end)

-- Override player left zone event
RegisterNetEvent('koth:playerLeftZone', function(team)
    local source = source
    if not team then return end
    
    -- Remove player from their team's zone list
    if kothZone.playersInZone[team] then
        kothZone.playersInZone[team][source] = nil
        print(('[KOTH ZONE FIX] Player %d left zone for team %s'):format(source, team))
    end
    
    -- Update controlling team
    local newControllingTeam = determineControllingTeam()
    if newControllingTeam ~= kothZone.controllingTeam then
        kothZone.controllingTeam = newControllingTeam
        print(('[KOTH ZONE FIX] Zone control changed to: %s'):format(newControllingTeam or 'contested'))
        
        -- Notify all clients
        TriggerClientEvent('koth:zoneControlChanged', -1, newControllingTeam)
    end
    
    -- Send zone status update
    local counts = getZoneCounts()
    TriggerClientEvent('koth:updateZoneStatus', -1, {
        controllingTeam = kothZone.controllingTeam,
        playersInZone = counts
    })
end)

-- Clean up when player disconnects
AddEventHandler('playerDropped', function()
    local source = source
    
    -- Remove from all zone lists
    for team, players in pairs(kothZone.playersInZone) do
        if players[source] then
            players[source] = nil
            print(('[KOTH ZONE FIX] Removed disconnected player %d from %s zone list'):format(source, team))
            
            -- Update controlling team
            local newControllingTeam = determineControllingTeam()
            if newControllingTeam ~= kothZone.controllingTeam then
                kothZone.controllingTeam = newControllingTeam
                TriggerClientEvent('koth:zoneControlChanged', -1, newControllingTeam)
            end
            
            break
        end
    end
end)

-- Point award system - runs every second to check 15-second intervals
Citizen.CreateThread(function()
    while true do
        if roundActive then
            local currentTime = GetGameTimer()
            
            -- Check if 15 seconds have passed since last point award
            if currentTime - kothZone.lastPointAward >= POINT_AWARD_INTERVAL then
                kothZone.lastPointAward = currentTime
                
                -- Award point to controlling team
                if kothZone.controllingTeam then
                    zonePoints[kothZone.controllingTeam] = zonePoints[kothZone.controllingTeam] + 1
                    
                    print(('[KOTH ZONE FIX] Awarded 1 point to %s team (Total: %d)'):format(
                        kothZone.controllingTeam, 
                        zonePoints[kothZone.controllingTeam]
                    ))
                    
                    -- Update all clients with new points
                    TriggerClientEvent('koth:updateZonePoints', -1, zonePoints)
                    
                    -- Check for round win
                    if zonePoints[kothZone.controllingTeam] >= POINTS_TO_WIN then
                        print(('[KOTH ZONE FIX] %s team wins with %d points!'):format(
                            kothZone.controllingTeam:upper(),
                            zonePoints[kothZone.controllingTeam]
                        ))
                        
                        -- End the round
                        EndKothRound(kothZone.controllingTeam)
                    end
                end
            end
        end
        
        Citizen.Wait(1000) -- Check every second
    end
end)

-- End round function
function EndKothRound(winningTeam)
    roundActive = false
    
    -- Announce winner
    TriggerClientEvent('chat:addMessage', -1, {
        color = {255, 215, 0},
        multiline = true,
        args = {'KOTH', ('^2%s TEAM WINS THE ROUND! ^7(150 Points Reached)'):format(winningTeam:upper())}
    })
    
    -- Trigger client-side round end
    TriggerClientEvent('koth:roundEnded', -1, winningTeam)
    
    -- Wait 5 seconds before resetting
    Citizen.SetTimeout(5000, function()
        -- Reset all players to their spawns
        for _, playerId in ipairs(GetPlayers()) do
            local playerIdNum = tonumber(playerId)
            local playerTeam = GetPlayerTeam(playerIdNum)
            
            if playerTeam then
                -- Get team spawn
                local teamSpawns = {
                    red = { x = 2238.15, y = 3788.91, z = 35.89, heading = 120.5 },
                    blue = { x = 1323.77, y = 3143.33, z = 40.41, heading = 282.48 },
                    green = { x = 1865.99, y = 2607.15, z = 45.67, heading = 276.45 }
                }
                
                local spawn = teamSpawns[playerTeam]
                if spawn then
                    -- Respawn player at team base
                    TriggerClientEvent('koth:forceRespawn', playerIdNum, spawn)
                end
            end
        end
        
        -- Delete all vehicles
        TriggerClientEvent('koth:deleteAllVehicles', -1)
        
        -- Reset round after another 5 seconds
        Citizen.SetTimeout(5000, function()
            ResetKothRound()
        end)
    end)
end

-- Reset round function
function ResetKothRound()
    -- Reset zone control
    kothZone.controllingTeam = nil
    kothZone.playersInZone = { red = {}, blue = {}, green = {} }
    kothZone.lastPointAward = GetGameTimer()
    
    -- Reset points
    zonePoints = { red = 0, blue = 0, green = 0 }
    
    -- Reactivate round
    roundActive = true
    
    -- Notify all clients
    TriggerClientEvent('koth:roundReset', -1)
    TriggerClientEvent('koth:updateZonePoints', -1, zonePoints)
    TriggerClientEvent('koth:zoneControlChanged', -1, nil)
    
    -- Announce new round
    TriggerClientEvent('chat:addMessage', -1, {
        color = {0, 255, 0},
        multiline = true,
        args = {'KOTH', 'New round started! First to 150 points wins!'}
    })
    
    print('[KOTH ZONE FIX] Round reset - ready for new round')
end

-- Command to check zone status (for debugging)
RegisterCommand('zonestatus', function(source)
    local counts = getZoneCounts()
    local message = string.format(
        'Zone Status - Control: %s | Red: %d | Blue: %d | Green: %d | Points - Red: %d | Blue: %d | Green: %d',
        kothZone.controllingTeam or 'Contested',
        counts.red, counts.blue, counts.green,
        zonePoints.red, zonePoints.blue, zonePoints.green
    )
    
    if source == 0 then
        print('[KOTH] ' .. message)
    else
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 255, 0},
            args = {'KOTH', message}
        })
    end
end, false)

-- Command to manually reset round (admin only)
RegisterCommand('resetround', function(source)
    if source == 0 or IsPlayerAceAllowed(source, 'koth.admin') then
        print('[KOTH ZONE FIX] Manual round reset requested')
        ResetKothRound()
    end
end, true)

-- Initialize on resource start
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() ~= resourceName then return end
    
    print('[KOTH ZONE FIX] Initializing KOTH zone system')
    
    -- Send initial zone points to all players
    Citizen.SetTimeout(2000, function()
        TriggerClientEvent('koth:updateZonePoints', -1, zonePoints)
    end)
end)

print('[KOTH ZONE FIX] Zone capture system loaded')
