* { box-sizing: border-box; margin: 0; padding: 0; }
html, body { width: 100%; height: 100%; font-family: 'Segoe UI', sans-serif; }
#overlay { position: fixed; inset: 0; background: rgba(0,0,0,0.85); display: none; align-items: center; justify-content: center; }
#team-select, #menu-container { display: none; flex-direction: column; align-items: center; }

/* Team Select */
#team-select { text-align: center; }
#team-select h1 { color: #fff; font-size: 2.5rem; margin-bottom: 20px; }
.team-buttons { display: flex; gap: 20px; }
.team-btn { padding: 20px 30px; font-size: 1.5rem; border: 2px solid #444; border-radius: 8px; background: #2e2e2e; color: #fff; cursor: pointer; transition: background 0.2s; }
.team-btn:hover { background: #3c3c3c; }
.team-btn[data-team="red"]   { border-color: #e74c3c; }
.team-btn[data-team="blue"]  { border-color: #3498db; }
.team-btn[data-team="green"] { border-color: #2ecc71; }
.team-count { margin-left: 8px; background: rgba(255,255,255,0.1); padding: 3px 6px; border-radius: 4px; font-size: 1rem; }

/* Menu Container */
#menu-container { background: #1e1e1e; padding: 20px; border-radius: 8px; width: 80%; max-width: 600px; color: #fff; position: relative; }
#close-btn { position: absolute; top: 10px; right: 10px; background: transparent; border: none; color: #fff; font-size: 1.5rem; cursor: pointer; }
#menu-title { text-align: center; margin-bottom: 20px; font-size: 2rem; }
#items { display: grid; grid-template-columns: repeat(auto-fill, minmax(120px,1fr)); gap: 15px; }
.item-card { background: #2a2a2a; padding: 10px; border-radius: 6px; display: flex; flex-direction: column; align-items: center; text-align: center; }
.item-card img { width: 80px; height: 80px; object-fit: cover; margin-bottom: 10px; }
.item-card .label { font-size: 1rem; margin-bottom: 5px; }
.item-card .action-btn { margin-top: auto; padding: 5px 8px; border: none; border-radius: 4px; cursor: pointer; background: #3498db; color: #fff; }
.item-card .action-btn.secondary { background: #27ae60; }

/* WEAPONS SHOP STYLING */
#weapons-shop {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  color: white;
  font-family: 'Arial', sans-serif;
  overflow: hidden;
}

.shop-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid #333;
}

.shop-title {
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 2px;
  color: #fff;
}

.shop-money {
  font-size: 20px;
  font-weight: bold;
  color: #00ff00;
  background: rgba(0, 0, 0, 0.5);
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid #333;
}

.shop-search {
  display: flex;
  align-items: center;
  padding: 20px 40px;
  gap: 10px;
}

#weapon-search {
  flex: 1;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid #333;
  border-radius: 5px;
  padding: 12px 16px;
  color: white;
  font-size: 14px;
}

#weapon-search::placeholder {
  color: #666;
}

.refresh-btn {
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid #333;
  border-radius: 5px;
  padding: 12px;
  color: white;
  cursor: pointer;
  font-size: 16px;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.weapons-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 20px;
  padding: 20px 40px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.weapon-card {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid #333;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.weapon-card:hover {
  border-color: #555;
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.weapon-image {
  width: 100%;
  height: 80px;
  object-fit: contain;
  margin-bottom: 10px;
  filter: brightness(0.9);
}

.weapon-name {
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8px;
  min-height: 20px;
}

.weapon-price {
  font-size: 16px;
  font-weight: bold;
  color: #00ff00;
  margin-bottom: 12px;
}

.weapon-buy-btn {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid #444;
  border-radius: 20px;
  padding: 8px 16px;
  color: white;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
}

.weapon-buy-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #666;
}

.weapon-buy-btn.disabled {
  background: #666;
  cursor: not-allowed;
  opacity: 0.5;
}

.weapon-buy-btn.disabled:hover {
  background: #666;
  border-color: #444;
}

.weapon-card.unaffordable {
  opacity: 0.6;
  filter: grayscale(50%);
}

.shop-close {
  position: absolute;
  top: 20px;
  right: 40px;
  background: rgba(255, 0, 0, 0.2);
  border: 1px solid #ff0000;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  color: #ff0000;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.shop-close:hover {
  background: rgba(255, 0, 0, 0.4);
}

/* VEHICLES SHOP STYLING (Same as weapons shop) */
#vehicles-shop {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  color: white;
  font-family: 'Arial', sans-serif;
  overflow: hidden;
}

.vehicles-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 20px;
  padding: 20px 40px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.vehicle-card {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid #333;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.vehicle-card:hover {
  border-color: #555;
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
}

.vehicle-image {
  width: 100%;
  height: 80px;
  object-fit: contain;
  margin-bottom: 10px;
  filter: brightness(0.9);
}

.vehicle-name {
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8px;
  min-height: 20px;
}

.vehicle-price {
  font-size: 16px;
  font-weight: bold;
  color: #00ff00;
  margin-bottom: 8px;
}

.vehicle-rent-price {
  font-size: 14px;
  color: #ffaa00;
  margin-bottom: 12px;
}

.vehicle-buttons {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.vehicle-buy-btn, .vehicle-rent-btn {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid #444;
  border-radius: 20px;
  padding: 8px 16px;
  color: white;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
}

.vehicle-buy-btn:hover, .vehicle-rent-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #666;
}

.vehicle-rent-btn {
  background: rgba(255, 165, 0, 0.2);
  border-color: #ffaa00;
  color: #ffaa00;
}

.vehicle-rent-btn:hover {
  background: rgba(255, 165, 0, 0.3);
}

.vehicle-buy-btn.disabled, .vehicle-rent-btn.disabled {
  background: #666;
  cursor: not-allowed;
  opacity: 0.5;
  color: #999;
}

.vehicle-buy-btn.disabled:hover, .vehicle-rent-btn.disabled:hover {
  background: #666;
  border-color: #444;
}

.vehicle-card.unaffordable {
  opacity: 0.6;
  filter: grayscale(50%);
}

/* CLASSES SELECTION UI */
#classes-selection {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, rgba(10, 10, 10, 0.95) 0%, rgba(26, 26, 26, 0.95) 100%);
  color: white;
  font-family: 'Arial', sans-serif;
  overflow: hidden;
}

.classes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px 50px;
  position: relative;
}

.classes-title {
  font-size: 28px;
  font-weight: normal;
  color: #fff;
  margin: 0;
  letter-spacing: 1px;
}

.classes-close {
  background: rgba(255, 0, 0, 0.2);
  border: 1px solid #ff0000;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  color: #ff0000;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.classes-close:hover {
  background: rgba(255, 0, 0, 0.4);
}

.classes-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 30px;
  padding: 50px;
  height: calc(100vh - 150px);
}

.class-card {
  background: rgba(0, 0, 0, 0.6);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  width: 200px;
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.class-card:hover {
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-5px);
  background: rgba(0, 0, 0, 0.7);
}

.class-card.selected {
  border-color: #00aaff;
  background: rgba(0, 170, 255, 0.1);
}

.class-card.locked {
  opacity: 0.5;
  cursor: not-allowed;
  filter: grayscale(70%);
}

.class-card.locked:hover {
  transform: none;
  background: rgba(0, 0, 0, 0.5);
  border-color: rgba(255, 255, 255, 0.1);
}

.class-card.medic {
  border-color: rgba(0, 170, 255, 0.5);
}

.class-card.medic:hover {
  border-color: #00aaff;
  background: rgba(0, 170, 255, 0.2);
}

.class-image {
  width: 120px;
  height: 150px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 15px;
  filter: brightness(0.8);
}

.class-info {
  text-align: center;
  width: 100%;
}

.class-name {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8px;
  text-transform: capitalize;
}

.class-name.medic {
  color: #00aaff;
}

.class-unlock {
  font-size: 12px;
  color: #888;
  margin-bottom: 15px;
}

.class-lock-icon {
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  font-size: 12px;
  color: #666;
}

/* Responsive design for smaller screens */
@media (max-width: 1200px) {
  .classes-container {
    gap: 20px;
    padding: 30px;
  }

  .class-card {
    width: 180px;
    height: 280px;
  }
}

@media (max-width: 900px) {
  .classes-container {
    flex-wrap: wrap;
    gap: 15px;
  }

  .class-card {
    width: 160px;
    height: 260px;
  }
}

/* PERMANENT GAME HUD SYSTEM */
#game-hud {
  position: fixed;
  bottom: 20px;
  right: 20px;
  color: white;
  font-family: 'Arial', sans-serif;
  z-index: 1000;
  display: block; /* Always visible */
}

/* Zone Control Points (Top Section) */
.zone-points {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  justify-content: flex-end;
}

.zone-box {
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid;
  border-radius: 6px;
  padding: 8px 12px;
  text-align: center;
  min-width: 50px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

.red-zone {
  border-color: #dc3545;
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.2) 0%, rgba(0, 0, 0, 0.8) 100%);
}

.green-zone {
  border-color: #28a745;
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.2) 0%, rgba(0, 0, 0, 0.8) 100%);
}

.blue-zone {
  border-color: #007bff;
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.2) 0%, rgba(0, 0, 0, 0.8) 100%);
}

.zone-number {
  font-size: 18px;
  font-weight: bold;
  color: white;
  line-height: 1;
  margin-bottom: 2px;
}

.zone-label {
  font-size: 10px;
  font-weight: bold;
  color: #ccc;
  letter-spacing: 1px;
}

/* Player Info Section */
.player-info {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 10px 15px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

.player-name {
  font-size: 14px;
  font-weight: bold;
  color: white;
  margin-bottom: 4px;
}

.player-money {
  font-size: 16px;
  font-weight: bold;
  color: #28a745;
  margin-bottom: 6px;
}

.player-stats {
  display: flex;
  gap: 15px;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-icon {
  font-size: 12px;
}

.stat-value {
  font-size: 11px;
  color: #ccc;
  font-weight: bold;
}

/* Team Player Counts in Player Info */
.team-player-counts {
  display: flex;
  gap: 12px;
  margin-top: 8px;
  padding-top: 6px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.team-players-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.team-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.red-dot { background: #dc3545; }
.green-dot { background: #28a745; }
.blue-dot { background: #007bff; }

.team-count-text {
  font-size: 10px;
  color: #ccc;
  font-weight: bold;
  min-width: 12px;
}

/* Health Bar Section */
.health-section {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.health-bar {
  flex: 1;
  height: 12px;
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  overflow: hidden;
}

.health-fill {
  height: 100%;
  background: linear-gradient(90deg, #dc3545 0%, #ff6b6b 100%);
  width: 100%;
  transition: width 0.3s ease;
  border-radius: 5px;
}

.health-text {
  font-size: 12px;
  font-weight: bold;
  color: white;
  min-width: 25px;
  text-align: right;
}

/* KOTH Zone Status (When Active) */
.koth-zone-status {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 215, 0, 0.5);
  border-radius: 6px;
  padding: 8px 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  display: none; /* Hidden by default, shown when in KOTH zone */
}

.koth-zone-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 6px;
}

.koth-crown {
  font-size: 12px;
  filter: drop-shadow(0 0 3px rgba(255, 215, 0, 0.5));
}

.koth-zone-name {
  font-size: 11px;
  font-weight: bold;
  color: #FFD700;
  letter-spacing: 1px;
}

.koth-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.koth-progress-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.koth-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FFD700 0%, #FFA500 100%);
  width: 0%;
  transition: width 0.3s ease;
  border-radius: 4px;
}

.koth-progress-fill.red {
  background: linear-gradient(90deg, #dc3545 0%, #ff6b6b 100%);
}

.koth-progress-fill.blue {
  background: linear-gradient(90deg, #007bff 0%, #66b3ff 100%);
}

.koth-progress-fill.green {
  background: linear-gradient(90deg, #28a745 0%, #66ff66 100%);
}

.koth-progress-text {
  font-size: 10px;
  font-weight: bold;
  color: white;
  min-width: 50px;
  text-align: right;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  #game-hud {
    bottom: 15px;
    right: 15px;
  }
}

@media (max-width: 900px) {
  #game-hud {
    bottom: 10px;
    right: 10px;
  }

  .zone-points {
    gap: 6px;
  }

  .zone-box {
    padding: 6px 10px;
    min-width: 45px;
  }

  .zone-number {
    font-size: 16px;
  }

  .team-player-counts {
    gap: 8px;
  }
}

/* DEATH SCREEN SYSTEM */
#death-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  font-family: 'Arial', sans-serif;
}

.death-content {
  text-align: center;
  color: white;
  max-width: 600px;
  padding: 40px;
}

.death-title {
  font-size: 48px;
  font-weight: bold;
  color: white;
  margin-bottom: 40px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  letter-spacing: 2px;
}

.respawn-section {
  margin-bottom: 30px;
}

.respawn-instruction {
  font-size: 18px;
  color: white;
  margin-bottom: 15px;
  font-weight: bold;
}

.key-highlight {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-weight: bold;
}

.respawn-progress {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.respawn-bar {
  width: 100%;
  height: 20px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.respawn-fill {
  height: 100%;
  background: linear-gradient(90deg, #dc3545 0%, #ff6b6b 100%);
  width: 0%;
  transition: width 0.1s linear;
  border-radius: 10px;
}

.bleedout-section {
  margin-bottom: 40px;
}

.bleedout-text {
  font-size: 20px;
  color: white;
  font-weight: bold;
  margin-bottom: 8px;
}

.medic-call {
  font-size: 14px;
  color: #ccc;
}

.medic-highlight {
  color: #28a745;
  font-weight: bold;
  text-transform: uppercase;
}

.killer-info {
  position: absolute;
  bottom: 80px;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  text-align: center;
}

.killer-text {
  font-size: 16px;
  color: #ccc;
}

.killer-id {
  color: #007bff;
  font-weight: bold;
}

.killer-name {
  color: #007bff;
  font-weight: bold;
}

/* Responsive adjustments for death screen */
@media (max-width: 900px) {
  .death-title {
    font-size: 36px;
  }

  .respawn-instruction {
    font-size: 16px;
  }

  .bleedout-text {
    font-size: 18px;
  }

  .killer-info {
    bottom: 60px;
  }
}

/* KILL REWARD POPUP SYSTEM */
#kill-reward-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(20, 20, 20, 0.95) 100%);
  border: 2px solid #ffd700;
  border-radius: 15px;
  padding: 30px;
  display: none;
  z-index: 10000;
  box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
  animation: rewardSlideIn 0.5s ease-out;
  min-width: 350px;
  text-align: center;
}

.reward-content {
  color: white;
  font-family: 'Arial', sans-serif;
}

.reward-title {
  font-size: 28px;
  font-weight: bold;
  color: #ffd700;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  letter-spacing: 2px;
}

.reward-victim {
  font-size: 16px;
  color: #ccc;
  margin-bottom: 25px;
}

.reward-items {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
  gap: 20px;
}

.reward-item {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 15px 20px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  flex: 1;
}

.reward-icon {
  font-size: 24px;
  margin-right: 12px;
}

.reward-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.reward-amount {
  font-size: 20px;
  font-weight: bold;
  color: #00ff00;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.reward-label {
  font-size: 12px;
  color: #ccc;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.xp-reward {
  border-left: 4px solid #ff6b35;
}

.money-reward {
  border-left: 4px solid #28a745;
}

.zone-bonus {
  background: linear-gradient(90deg, #ff6b35, #f7931e);
  padding: 12px;
  border-radius: 8px;
  margin-top: 15px;
  display: none;
  animation: zonePulse 1s infinite alternate;
}

.zone-text {
  font-size: 16px;
  font-weight: bold;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

/* LEVEL UP POPUP */
#levelup-popup {
  position: fixed;
  top: 30%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.95) 0%, rgba(75, 0, 130, 0.95) 100%);
  border: 3px solid #ffd700;
  border-radius: 20px;
  padding: 40px;
  display: none;
  z-index: 10001;
  box-shadow: 0 0 40px rgba(255, 215, 0, 0.7);
  animation: levelUpBounce 0.8s ease-out;
  text-align: center;
}

.levelup-content {
  color: white;
  font-family: 'Arial', sans-serif;
}

.levelup-title {
  font-size: 36px;
  font-weight: bold;
  color: #ffd700;
  margin-bottom: 20px;
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
  letter-spacing: 3px;
}

.levelup-text {
  font-size: 28px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.level-old {
  color: #ccc;
  font-weight: bold;
}

.level-arrow {
  color: #ffd700;
  font-size: 32px;
}

.level-new {
  color: #00ff00;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 255, 0, 0.5);
}

.levelup-subtitle {
  font-size: 16px;
  color: #ddd;
  font-style: italic;
}

/* ANIMATIONS */
@keyframes rewardSlideIn {
  from {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

@keyframes levelUpBounce {
  0% {
    transform: translate(-50%, -50%) scale(0.3);
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

@keyframes zonePulse {
  from {
    box-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
  }
  to {
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.8);
  }
}

/* WEAPON HOTBAR */
#weapon-hotbar {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 100;
}

.hotbar-slot {
  width: 80px;
  height: 80px;
  background-color: rgba(0, 0, 0, 0.8);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.hotbar-slot:hover {
  background-color: rgba(0, 0, 0, 0.9);
  border-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.hotbar-slot.active {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.slot-number {
  position: absolute;
  top: 5px;
  right: 8px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  font-weight: bold;
  font-family: Arial, sans-serif;
}

.weapon-icon {
  width: 60px;
  height: 40px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  filter: brightness(0) invert(1);
  opacity: 0.8;
}

.hotbar-slot.active .weapon-icon {
  opacity: 1;
}

.hotbar-slot.empty .weapon-icon {
  opacity: 0.2;
}

.ammo-count {
  position: absolute;
  bottom: 5px;
  right: 8px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  font-weight: bold;
  font-family: Arial, sans-serif;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

/* Weapon icon styles for different weapons */
.weapon-icon.unarmed {
  background-image: url('images/guns/unarmed.png');
}

.weapon-icon.stone_hatchet {
  background-image: url('images/guns/stone_hatchet.png');
}

.weapon-icon.crowbar {
  background-image: url('images/guns/crowbar.png');
}

.weapon-icon.hatchet {
  background-image: url('images/guns/hatchet.png');
}

.weapon-icon.pistol {
  background-image: url('images/guns/pistol.png');
}

.weapon-icon.micro_smg {
  background-image: url('images/guns/micro_smg.png');
}

.weapon-icon.combat_pdw {
  background-image: url('images/guns/combat_pdw.png');
}

.weapon-icon.assault_smg {
  background-image: url('images/guns/assault_smg.png');
}

.weapon-icon.bullpup_rifle {
  background-image: url('images/guns/bullpup_rifle.png');
}

.weapon-icon.heavy_rifle {
  background-image: url('images/guns/heavy_rifle.png');
}

.weapon-icon.assault_rifle {
  background-image: url('images/guns/assault_rifle.png');
}

.weapon-icon.special_carbine {
  background-image: url('images/guns/special_carbine.png');
}

.weapon-icon.special_carbine_mk2 {
  background-image: url('images/guns/special_carbine_mk2.png');
}

.weapon-icon.carbine_rifle_mk2 {
  background-image: url('images/guns/carbine_rifle_mk2.png');
}
