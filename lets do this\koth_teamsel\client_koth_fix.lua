-- CLIENT KOTH ZONE FIX: Handle round endings and vehicle deletion

local roundActive = true

-- Handle forced respawn at round end
RegisterNetEvent('koth:forceRespawn', function(spawnData)
    print('[KOTH ZONE FIX] Forcing respawn to team base')
    
    local playerPed = PlayerPedId()
    
    -- Remove all weapons
    RemoveAllPedWeapons(playerPed, true)
    
    -- Respawn at team base
    SetEntityCoords(playerPed, spawnData.x, spawnData.y, spawnData.z, false, false, false, true)
    SetEntityHeading(playerPed, spawnData.heading or 0.0)
    
    -- Ensure player is alive and can move
    if IsEntityDead(playerPed) then
        NetworkResurrectLocalPlayer(spawnData.x, spawnData.y, spawnData.z, spawnData.heading, true, false)
    end
    
    ClearPedTasksImmediately(playerPed)
    FreezeEntityPosition(playerPed, false)
    
    -- Show notification
    BeginTextCommandThefeedPost("STRING")
    AddTextComponentSubstringPlayerName("Round ended! Respawned at team base with no weapons.")
    EndTextCommandThefeedPostTicker(false, true)
end)

-- Handle vehicle deletion at round end
RegisterNetEvent('koth:deleteAllVehicles', function()
    print('[KOTH ZONE FIX] Deleting all vehicles')
    
    -- Get all vehicles
    local vehicles = GetGamePool('CVehicle')
    local deletedCount = 0
    
    for _, vehicle in ipairs(vehicles) do
        if DoesEntityExist(vehicle) then
            -- Check if vehicle has a driver
            local driver = GetPedInVehicleSeat(vehicle, -1)
            if driver and driver ~= 0 then
                -- Eject driver
                TaskLeaveVehicle(driver, vehicle, 4160)
            end
            
            -- Delete the vehicle
            SetEntityAsMissionEntity(vehicle, true, true)
            DeleteVehicle(vehicle)
            deletedCount = deletedCount + 1
        end
    end
    
    print(('[KOTH ZONE FIX] Deleted %d vehicles'):format(deletedCount))
end)

-- Handle round end announcement
RegisterNetEvent('koth:roundEnded', function(winningTeam)
    print(('[KOTH ZONE FIX] Round ended - Winner: %s'):format(winningTeam))
    roundActive = false
    
    -- Play victory sound
    PlaySoundFrontend(-1, "CHECKPOINT_PERFECT", "HUD_MINI_GAME_SOUNDSET", true)
    
    -- Show big notification
    local scaleform = RequestScaleformMovie("MP_BIG_MESSAGE_FREEMODE")
    while not HasScaleformMovieLoaded(scaleform) do
        Citizen.Wait(0)
    end
    
    -- Display winner message
    BeginScaleformMovieMethod(scaleform, "SHOW_SHARD_WASTED_MP_MESSAGE")
    PushScaleformMovieMethodParameterString(winningTeam:upper() .. " TEAM WINS!")
    PushScaleformMovieMethodParameterString("First to 150 points!")
    EndScaleformMovieMethod()
    
    -- Show for 5 seconds
    local endTime = GetGameTimer() + 5000
    while GetGameTimer() < endTime do
        DrawScaleformMovieFullscreen(scaleform, 255, 255, 255, 255, 0)
        Citizen.Wait(0)
    end
    
    SetScaleformMovieAsNoLongerNeeded(scaleform)
end)

-- Handle round reset
RegisterNetEvent('koth:roundReset', function()
    print('[KOTH ZONE FIX] Round reset')
    roundActive = true
    
    -- Play start sound
    PlaySoundFrontend(-1, "CHECKPOINT_NORMAL", "HUD_MINI_GAME_SOUNDSET", true)
    
    -- Show notification
    BeginTextCommandThefeedPost("STRING")
    AddTextComponentSubstringPlayerName("New round started! Get to the KOTH zone!")
    EndTextCommandThefeedPostTicker(false, true)
end)

-- Update zone points display
RegisterNetEvent('koth:updateZonePoints', function(points)
    if points then
        -- Update the HUD with zone points
        SendNUIMessage({
            action = 'updateZonePoints',
            points = points
        })
        
        -- Also update the specific zone point elements
        SendNUIMessage({
            action = 'updateZoneScores',
            red = points.red or 0,
            green = points.green or 0,
            blue = points.blue or 0
        })
    end
end)

-- Override the zone status update to show controlling team
RegisterNetEvent('koth:updateZoneStatus', function(status)
    if status then
        -- Update HUD with zone control info
        local controlText = "Contested"
        local controlColor = "#808080" -- Grey for contested
        
        if status.controllingTeam then
            controlText = status.controllingTeam:upper() .. " Controls"
            if status.controllingTeam == "red" then
                controlColor = "#ff0000"
            elseif status.controllingTeam == "blue" then
                controlColor = "#0064ff"
            elseif status.controllingTeam == "green" then
                controlColor = "#00ff00"
            end
        end
        
        -- Update zone control display
        SendNUIMessage({
            action = 'updateKothZoneControl',
            controlling = status.controllingTeam,
            text = controlText,
            color = controlColor,
            playersInZone = status.playersInZone
        })
    end
end)

-- Show zone control notification when it changes
RegisterNetEvent('koth:zoneControlChanged', function(newTeam)
    if newTeam then
        BeginTextCommandThefeedPost("STRING")
        AddTextComponentSubstringPlayerName(newTeam:upper() .. " team has taken control of the KOTH zone!")
        EndTextCommandThefeedPostTicker(false, true)
        
        -- Play capture sound
        PlaySoundFrontend(-1, "CHECKPOINT_AHEAD", "HUD_MINI_GAME_SOUNDSET", true)
    else
        BeginTextCommandThefeedPost("STRING")
        AddTextComponentSubstringPlayerName("KOTH zone is now contested!")
        EndTextCommandThefeedPostTicker(false, true)
    end
end)

-- Debug command to check round status
RegisterCommand('roundstatus', function()
    print(('[KOTH ZONE FIX] Round active: %s'):format(roundActive and 'YES' or 'NO'))
end, false)

print('[KOTH ZONE FIX] Client zone system loaded')
