// UI MANAGEMENT FIX: Ensure all UI elements are properly hidden

// Track active UI elements
let activeUIElements = new Set();

// Function to hide all UI elements
function hideAllUIElements() {
    console.log('[KOTH UI FIX] Hiding all UI elements');
    
    // Hide team selection
    const teamSelect = document.getElementById('team-select');
    if (teamSelect) {
        teamSelect.style.display = 'none';
        teamSelect.style.visibility = 'hidden';
    }
    
    // Hide menu container
    const menuContainer = document.getElementById('menu-container');
    if (menuContainer) {
        menuContainer.style.display = 'none';
        menuContainer.style.visibility = 'hidden';
    }
    
    // Hide weapons shop
    const weaponsShop = document.getElementById('weapons-shop');
    if (weaponsShop) {
        weaponsShop.style.display = 'none';
        weaponsShop.style.visibility = 'hidden';
    }
    
    // Hide vehicles shop
    const vehiclesShop = document.getElementById('vehicles-shop');
    if (vehiclesShop) {
        vehiclesShop.style.display = 'none';
        vehiclesShop.style.visibility = 'hidden';
    }
    
    // Hide classes selection
    const classesSelection = document.getElementById('classes-selection');
    if (classesSelection) {
        classesSelection.style.display = 'none';
        classesSelection.style.visibility = 'hidden';
    }
    
    // Hide death screen
    const deathScreen = document.getElementById('death-screen');
    if (deathScreen) {
        deathScreen.style.display = 'none';
        deathScreen.style.visibility = 'hidden';
    }
    
    // Hide overlay if all elements are hidden
    const overlay = document.getElementById('overlay');
    if (overlay) {
        overlay.style.display = 'none';
        overlay.style.visibility = 'hidden';
    }
    
    // Clear active UI tracking
    activeUIElements.clear();
    
    // Notify Lua that all UI is closed
    fetch(`https://${GetParentResourceName()}/uiClosed`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'all' })
    });
}

// Override message handler to track UI states
window.addEventListener('message', function(event) {
    const data = event.data;
    
    // Handle hideAll action
    if (data.action === 'hideAll' || data.action === 'forceCloseAll') {
        console.log('[KOTH UI FIX] Received hideAll command');
        hideAllUIElements();
        return;
    }
    
    // Handle specific hide actions
    const hideActions = [
        'hideTeamSelect',
        'hideMenu',
        'hideWeaponSelect',
        'hideDeathScreen',
        'hideVehicleShop',
        'hideClassSelection'
    ];
    
    if (hideActions.includes(data.action)) {
        console.log('[KOTH UI FIX] Hiding specific UI:', data.action);
        hideAllUIElements();
        return;
    }
    
    // Track when UIs are shown
    if (data.action === 'showTeamSelect') {
        activeUIElements.add('teamSelect');
        // Ensure overlay is visible
        const overlay = document.getElementById('overlay');
        if (overlay) {
            overlay.style.display = 'block';
            overlay.style.visibility = 'visible';
        }
    }
    
    if (data.action === 'showMenu') {
        activeUIElements.add('menu');
        const overlay = document.getElementById('overlay');
        if (overlay) {
            overlay.style.display = 'block';
            overlay.style.visibility = 'visible';
        }
    }
});

// Ensure team selection properly hides after selection
document.addEventListener('DOMContentLoaded', function() {
    console.log('[KOTH UI FIX] DOM loaded, setting up UI management');
    
    // Override team selection cards
    const teamCards = document.querySelectorAll('.team-card');
    teamCards.forEach(card => {
        card.addEventListener('click', function() {
            console.log('[KOTH UI FIX] Team card clicked, scheduling UI cleanup');
            
            // Hide UI after a short delay to allow selection to process
            setTimeout(() => {
                hideAllUIElements();
            }, 500);
        });
    });
    
    // Override close buttons
    const closeButtons = document.querySelectorAll('#close-btn, .shop-close, .classes-close');
    closeButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('[KOTH UI FIX] Close button clicked');
            hideAllUIElements();
        });
    });
    
    // Add escape key handler
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            console.log('[KOTH UI FIX] Escape key pressed');
            hideAllUIElements();
        }
    });
    
    // Ensure game HUD is always visible
    const gameHud = document.getElementById('game-hud');
    if (gameHud) {
        gameHud.style.display = 'block';
        gameHud.style.visibility = 'visible';
        gameHud.style.zIndex = '1000'; // Lower than UI overlays
    }
});

// Periodic check to ensure no stuck UI
setInterval(function() {
    // If no active UI elements but overlay is visible, hide it
    if (activeUIElements.size === 0) {
        const overlay = document.getElementById('overlay');
        if (overlay && (overlay.style.display === 'block' || overlay.style.visibility === 'visible')) {
            console.log('[KOTH UI FIX] Found stuck overlay with no active UI, hiding...');
            overlay.style.display = 'none';
            overlay.style.visibility = 'hidden';
        }
    }
}, 2000); // Check every 2 seconds

console.log('[KOTH UI FIX] UI management script loaded');
