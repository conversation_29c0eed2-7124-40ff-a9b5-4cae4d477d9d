console.log('[KOTH Admin] Script loading...');

let currentPlayers = [];
let selectedPlayer = null;
let kothStatus = null;

// Handle messages from game
window.addEventListener('message', function(event) {
    const data = event.data;
    
    if (data.action === 'openPanel') {
        openAdminPanel(data);
    } else if (data.action === 'updateData') {
        updatePanelData(data);
    } else if (data.action === 'notification') {
        showNotification(data.type, data.message);
    } else if (data.action === 'closePanel') {
        closeAdminPanel();
    }
});

// Open admin panel
function openAdminPanel(data) {
    document.getElementById('admin-panel').style.display = 'block';
    
    if (data.players) {
        currentPlayers = data.players;
        renderPlayerList();
    }
    
    if (data.kothStatus) {
        kothStatus = data.kothStatus;
        updateKothStatus();
    }
}

// Close admin panel
function closeAdminPanel() {
    document.getElementById('admin-panel').style.display = 'none';
    document.getElementById('player-modal').style.display = 'none';
    
    fetch(`https://${GetParentResourceName()}/closePanel`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
    });
}

// Update panel data
function updatePanelData(data) {
    if (data.players) {
        currentPlayers = data.players;
        renderPlayerList();
    }
    
    if (data.kothStatus) {
        kothStatus = data.kothStatus;
        updateKothStatus();
    }
}

// Render player list
function renderPlayerList() {
    const playerList = document.getElementById('player-list');
    const searchTerm = document.getElementById('player-search').value.toLowerCase();
    
    playerList.innerHTML = '';
    
    const filteredPlayers = currentPlayers.filter(player => 
        player.name.toLowerCase().includes(searchTerm) ||
        player.id.toString().includes(searchTerm)
    );
    
    filteredPlayers.forEach(player => {
        const playerItem = document.createElement('div');
        playerItem.className = 'player-item';
        playerItem.onclick = () => openPlayerModal(player);
        
        playerItem.innerHTML = `
            <div class="player-header">
                <span class="player-name">${player.name}</span>
                <span class="player-id">ID: ${player.id}</span>
            </div>
            <div class="player-stats-row">
                <div class="stat">
                    <span class="stat-label">Money</span>
                    <span class="stat-value">$${player.money.toLocaleString()}</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Level</span>
                    <span class="stat-value">${player.level}</span>
                </div>
                <div class="stat">
                    <span class="stat-label">XP</span>
                    <span class="stat-value">${player.xp.toLocaleString()}</span>
                </div>
                <div class="stat">
                    <span class="stat-label">K/D</span>
                    <span class="stat-value">${player.kills}/${player.deaths}</span>
                </div>
            </div>
        `;
        
        playerList.appendChild(playerItem);
    });
}

// Update KOTH status display
function updateKothStatus() {
    if (!kothStatus) return;
    
    const statusElement = document.getElementById('round-status');
    const controllingTeam = document.getElementById('controlling-team');
    const redPoints = document.getElementById('red-points');
    const greenPoints = document.getElementById('green-points');
    const bluePoints = document.getElementById('blue-points');
    
    if (kothStatus.active) {
        statusElement.textContent = 'Active';
        statusElement.className = 'status-value active';
    } else {
        statusElement.textContent = 'Inactive';
        statusElement.className = 'status-value';
    }
    
    if (controllingTeam) {
        controllingTeam.textContent = kothStatus.controllingTeam || 'None';
        if (kothStatus.controllingTeam) {
            controllingTeam.style.color = getTeamColor(kothStatus.controllingTeam);
        }
    }
    
    if (kothStatus.zonePoints) {
        if (redPoints) redPoints.textContent = kothStatus.zonePoints.red || 0;
        if (greenPoints) greenPoints.textContent = kothStatus.zonePoints.green || 0;
        if (bluePoints) bluePoints.textContent = kothStatus.zonePoints.blue || 0;
    }
}

// Get team color
function getTeamColor(team) {
    switch(team) {
        case 'red': return '#ff4444';
        case 'green': return '#44ff44';
        case 'blue': return '#4444ff';
        default: return '#ffffff';
    }
}

// Open player modal
function openPlayerModal(player) {
    selectedPlayer = player;
    
    document.getElementById('modal-player-name').textContent = player.name;
    document.getElementById('modal-player-id').textContent = player.id;
    document.getElementById('modal-player-money').textContent = player.money.toLocaleString();
    document.getElementById('modal-player-level').textContent = player.level;
    document.getElementById('modal-player-xp').textContent = player.xp.toLocaleString();
    document.getElementById('modal-player-kills').textContent = player.kills;
    document.getElementById('modal-player-deaths').textContent = player.deaths;
    
    document.getElementById('player-modal').style.display = 'flex';
}

// Close player modal
function closePlayerModal() {
    document.getElementById('player-modal').style.display = 'none';
    selectedPlayer = null;
}

// Show notification
function showNotification(type, message) {
    const container = document.getElementById('notification-container');
    
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    container.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-out';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

// Event Listeners

// Close buttons
document.getElementById('close-panel').addEventListener('click', closeAdminPanel);
document.getElementById('close-modal').addEventListener('click', closePlayerModal);

// Search
document.getElementById('player-search').addEventListener('input', renderPlayerList);

// Refresh players
document.getElementById('refresh-players').addEventListener('click', function() {
    fetch(`https://${GetParentResourceName()}/refreshPlayers`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
    });
});

// KOTH Controls
document.getElementById('start-round').addEventListener('click', function() {
    if (confirm('Are you sure you want to start a new KOTH round?')) {
        fetch(`https://${GetParentResourceName()}/startRound`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
    }
});

document.getElementById('stop-round').addEventListener('click', function() {
    if (confirm('Are you sure you want to stop the current KOTH round?')) {
        fetch(`https://${GetParentResourceName()}/stopRound`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({})
        });
    }
});

// Player Actions
document.getElementById('give-money').addEventListener('click', function() {
    const amount = parseInt(document.getElementById('money-amount').value);
    
    if (!selectedPlayer || !amount || amount <= 0) {
        showNotification('error', 'Please enter a valid amount');
        return;
    }
    
    fetch(`https://${GetParentResourceName()}/giveMoney`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            playerId: selectedPlayer.id,
            amount: amount
        })
    });
    
    document.getElementById('money-amount').value = '';
});

document.getElementById('give-xp').addEventListener('click', function() {
    const amount = parseInt(document.getElementById('xp-amount').value);
    
    if (!selectedPlayer || !amount || amount <= 0) {
        showNotification('error', 'Please enter a valid amount');
        return;
    }
    
    fetch(`https://${GetParentResourceName()}/giveXP`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            playerId: selectedPlayer.id,
            amount: amount
        })
    });
    
    document.getElementById('xp-amount').value = '';
});

document.getElementById('set-level').addEventListener('click', function() {
    const level = parseInt(document.getElementById('level-amount').value);
    
    if (!selectedPlayer || !level || level < 1 || level > 50) {
        showNotification('error', 'Please enter a valid level (1-50)');
        return;
    }
    
    fetch(`https://${GetParentResourceName()}/setLevel`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            playerId: selectedPlayer.id,
            level: level
        })
    });
    
    document.getElementById('level-amount').value = '';
});

document.getElementById('reset-player').addEventListener('click', function() {
    if (!selectedPlayer) return;
    
    if (confirm(`Are you sure you want to reset all stats for ${selectedPlayer.name}? This cannot be undone!`)) {
        fetch(`https://${GetParentResourceName()}/resetPlayer`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                playerId: selectedPlayer.id
            })
        });
        
        closePlayerModal();
    }
});

// ESC key handler
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        if (document.getElementById('player-modal').style.display === 'flex') {
            closePlayerModal();
        } else {
            closeAdminPanel();
        }
    }
});

// Click outside modal to close
document.getElementById('player-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closePlayerModal();
    }
});

console.log('[KOTH Admin] Script loaded');
