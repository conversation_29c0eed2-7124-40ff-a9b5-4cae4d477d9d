-- =====================================================
-- KOTH DEATH SYSTEM - Clean Implementation
-- =====================================================
print('[KOTH DEATH] Loading death system...')

-- Death system variables
local DeathSystem = {
    isDead = false,
    deathTime = 0,
    killerServerId = nil,
    respawnHoldTime = 0,
    respawnProgress = 0,
    hasReleasedE = true,
    bleedoutDuration = 50000, -- 50 seconds in milliseconds
    respawnHoldDuration = 3000, -- 3 seconds to respawn
    
    -- Team spawn coordinates
    teamSpawns = {
        red = { x = 2238.15, y = 3788.91, z = 35.89, heading = 120.5 },
        blue = { x = 1323.77, y = 3143.33, z = 40.41, heading = 282.48 },
        green = { x = 1865.99, y = 2607.15, z = 45.67, heading = 276.45 }
    },
    
    -- KOTH zone for kill detection
    kothZone = {
        x = 2842.4216,
        y = 2864.8088,
        z = 62.5975,
        radius = 150.0
    }
}

-- =====================================================
-- DEATH ANIMATION SYSTEM
-- =====================================================
local function playDeathAnimation()
    local playerPed = PlayerPedId()
    
    -- Request animation dictionary
    RequestAnimDict("dead")
    while not HasAnimDictLoaded("dead") do
        Citizen.Wait(1)
    end
    
    -- Play death animation
    TaskPlayAnim(playerPed, "dead", "dead_a", 8.0, -8.0, -1, 1, 0, false, false, false)
    
    -- Set player to ragdoll for realistic death
    SetPedToRagdoll(playerPed, 1000, 1000, 0, true, true, false)
    
    print('[KOTH DEATH] Death animation applied')
end

-- =====================================================
-- KILLER DETECTION SYSTEM
-- =====================================================
local function getKillerInfo(playerPed)
    local killerPed = GetPedSourceOfDeath(playerPed)
    local killerServerId = nil
    
    if killerPed and killerPed ~= playerPed and IsPedAPlayer(killerPed) then
        killerServerId = GetPlayerServerId(NetworkGetPlayerIndexFromPed(killerPed))
    end
    
    return killerServerId
end

-- =====================================================
-- ZONE DETECTION SYSTEM
-- =====================================================
local function isPlayerInKothZone(playerCoords)
    local distance = #(playerCoords - vector3(DeathSystem.kothZone.x, DeathSystem.kothZone.y, DeathSystem.kothZone.z))
    return distance <= DeathSystem.kothZone.radius
end

-- =====================================================
-- RESPAWN SYSTEM
-- =====================================================
local function respawnPlayer()
    local playerPed = PlayerPedId()
    local playerTeam = GetResourceKvpString('playerTeam') or 'red'
    local spawn = DeathSystem.teamSpawns[playerTeam] or DeathSystem.teamSpawns.red
    
    print('[KOTH DEATH] Respawning player at team base:', playerTeam)
    
    -- Respawn player at team base
    NetworkResurrectLocalPlayer(spawn.x, spawn.y, spawn.z, spawn.heading, true, false)
    
    -- Clear death state
    DeathSystem.isDead = false
    DeathSystem.respawnHoldTime = 0
    DeathSystem.respawnProgress = 0
    DeathSystem.hasReleasedE = true
    
    -- Clear tasks and unfreeze
    ClearPedTasksImmediately(playerPed)
    FreezeEntityPosition(playerPed, false)
    
    -- Give basic weapon
    GiveWeaponToPed(playerPed, GetHashKey('WEAPON_PISTOL'), 250, false, true)
    
    -- Hide death screen
    SendNUIMessage({ action = 'hideDeathScreen' })
    
    -- Show respawn notification
    BeginTextCommandThefeedPost("STRING")
    AddTextComponentSubstringPlayerName("Respawned at team base")
    EndTextCommandThefeedPostTicker(false, true)
    
    print('[KOTH DEATH] Player respawned successfully')
end

-- =====================================================
-- DEATH SCREEN UI SYSTEM
-- =====================================================
local function showDeathScreen(killerServerId)
    local killerName = 'Unknown'
    local killerId = 0
    
    if killerServerId then
        killerName = GetPlayerName(GetPlayerFromServerId(killerServerId)) or 'Unknown'
        killerId = killerServerId
    end
    
    SendNUIMessage({
        action = 'showDeathScreen',
        killer = killerName,
        killerId = killerId
    })
    
    print('[KOTH DEATH] Death screen shown for killer:', killerName)
end

local function updateDeathTimer()
    local elapsedTime = (GetGameTimer() - DeathSystem.deathTime) / 1000
    local bleedoutRemaining = math.max(0, 50 - math.floor(elapsedTime))
    
    SendNUIMessage({
        action = 'updateDeathTimer',
        bleedoutTimer = bleedoutRemaining
    })
end

local function updateRespawnProgress()
    SendNUIMessage({
        action = 'updateRespawnProgress',
        progress = DeathSystem.respawnProgress
    })
end

-- =====================================================
-- MAIN DEATH DETECTION THREAD
-- =====================================================
Citizen.CreateThread(function()
    while true do
        local playerPed = PlayerPedId()
        
        -- Check if player just died
        if IsEntityDead(playerPed) and not DeathSystem.isDead then
            print('[KOTH DEATH] Player death detected')
            
            -- Set death state
            DeathSystem.isDead = true
            DeathSystem.deathTime = GetGameTimer()
            DeathSystem.respawnHoldTime = 0
            DeathSystem.respawnProgress = 0
            DeathSystem.hasReleasedE = false
            
            -- Disable auto-respawn
            NetworkResurrectLocalPlayer(GetEntityCoords(playerPed), GetEntityHeading(playerPed), false, false)
            
            -- Freeze player and play death animation
            FreezeEntityPosition(playerPed, true)
            playDeathAnimation()
            
            -- Get killer information
            DeathSystem.killerServerId = getKillerInfo(playerPed)
            
            -- Check if death was in KOTH zone
            local playerCoords = GetEntityCoords(playerPed)
            local inKothZone = isPlayerInKothZone(playerCoords)
            
            -- Report kill to server
            if DeathSystem.killerServerId then
                print('[KOTH DEATH] Reporting kill to server - Killer:', DeathSystem.killerServerId, 'In Zone:', inKothZone)
                TriggerServerEvent('koth:playerKilled', DeathSystem.killerServerId, GetPlayerServerId(PlayerId()), inKothZone)
            end
            
            -- Show death screen
            showDeathScreen(DeathSystem.killerServerId)
            
        elseif DeathSystem.isDead then
            -- Player is dead, handle death state
            local playerPed = PlayerPedId()
            
            -- Maintain death animation
            if not IsEntityPlayingAnim(playerPed, "dead", "dead_a", 3) then
                playDeathAnimation()
            end
            
            -- Keep player frozen
            FreezeEntityPosition(playerPed, true)
            
            -- Update death timer
            updateDeathTimer()
            
            -- Handle respawn input (E key)
            if IsControlPressed(0, 38) then -- E key
                if DeathSystem.hasReleasedE then
                    DeathSystem.respawnHoldTime = DeathSystem.respawnHoldTime + GetFrameTime() * 1000 -- Convert to milliseconds
                    DeathSystem.respawnProgress = math.min(100, (DeathSystem.respawnHoldTime / DeathSystem.respawnHoldDuration) * 100)
                    
                    -- Update progress bar
                    updateRespawnProgress()
                    
                    -- Respawn when held for required duration
                    if DeathSystem.respawnHoldTime >= DeathSystem.respawnHoldDuration then
                        respawnPlayer()
                    end
                end
            else
                -- E key released
                DeathSystem.hasReleasedE = true
                if DeathSystem.respawnHoldTime > 0 then
                    DeathSystem.respawnHoldTime = 0
                    DeathSystem.respawnProgress = 0
                    updateRespawnProgress()
                end
            end
            
            -- Auto-respawn after bleedout
            local elapsedTime = GetGameTimer() - DeathSystem.deathTime
            if elapsedTime >= DeathSystem.bleedoutDuration then
                print('[KOTH DEATH] Auto-respawning after bleedout')
                respawnPlayer()
            end
            
        elseif not IsEntityDead(playerPed) and not DeathSystem.isDead then
            -- Player is alive, ensure they're not stuck in injured state
            if IsPedFatallyInjured(playerPed) then
                ClearPedTasksImmediately(playerPed)
            end
        end
        
        Citizen.Wait(0)
    end
end)

-- =====================================================
-- INPUT CONTROL SYSTEM
-- =====================================================
Citizen.CreateThread(function()
    while true do
        if DeathSystem.isDead then
            -- Disable all controls except E key
            DisableControlAction(0, 7, true)   -- L
            DisableControlAction(0, 18, true)  -- Enter
            DisableControlAction(0, 22, true)  -- Space
            DisableControlAction(0, 176, true) -- Enter/Return
            DisableControlAction(0, 249, true) -- N
            
            -- Disable movement
            DisableControlAction(0, 30, true)  -- A/D
            DisableControlAction(0, 31, true)  -- W/S
            DisableControlAction(0, 32, true)  -- W
            DisableControlAction(0, 33, true)  -- S
            DisableControlAction(0, 34, true)  -- A
            DisableControlAction(0, 35, true)  -- D
            
            -- Disable camera movement
            DisableControlAction(0, 1, true)   -- Mouse look
            DisableControlAction(0, 2, true)   -- Mouse look
            
            -- Disable weapon controls
            DisableControlAction(0, 24, true)  -- Attack
            DisableControlAction(0, 25, true)  -- Aim
            DisableControlAction(0, 47, true)  -- Weapon wheel
            DisableControlAction(0, 58, true)  -- Weapon wheel
        end
        
        Citizen.Wait(0)
    end
end)

-- =====================================================
-- EVENT HANDLERS
-- =====================================================

-- Store player team when selected
RegisterNetEvent('koth:teamSelected', function(team)
    SetResourceKvp('playerTeam', team)
    print('[KOTH DEATH] Stored player team:', team)
end)

-- Override spawn event to store team
RegisterNetEvent('koth:spawnPlayer', function(spawnData)
    -- Extract team from spawn coordinates
    for team, spawn in pairs(DeathSystem.teamSpawns) do
        if math.abs(spawnData.x - spawn.x) < 1.0 and math.abs(spawnData.y - spawn.y) < 1.0 then
            SetResourceKvp('playerTeam', team)
            print('[KOTH DEATH] Detected and stored team from spawn:', team)
            break
        end
    end
end)

-- =====================================================
-- DEBUG COMMANDS
-- =====================================================
RegisterCommand('deathtest', function()
    if DeathSystem.isDead then
        print('[KOTH DEATH] Force respawning player')
        respawnPlayer()
    else
        print('[KOTH DEATH] Player is not dead')
    end
end, false)

RegisterCommand('deathstatus', function()
    print('[KOTH DEATH] Status - Dead:', DeathSystem.isDead, 'Team:', GetResourceKvpString('playerTeam') or 'none')
end, false)

print('[KOTH DEATH] Death system loaded successfully')
