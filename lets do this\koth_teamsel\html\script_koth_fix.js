// KOTH ZONE FIX: Properly update zone points and control status on HUD

// Handle zone point updates
window.addEventListener('message', function(event) {
    const data = event.data;
    
    // Update zone points (the numbers at the top)
    if (data.action === 'updateZonePoints' && data.points) {
        console.log('[KOTH ZONE FIX] Updating zone points:', data.points);
        
        // Update red zone points
        const redPoints = document.getElementById('red-zone-points');
        if (redPoints) {
            redPoints.textContent = data.points.red || 0;
            // Flash effect when points change
            redPoints.style.animation = 'pulse 0.5s';
            setTimeout(() => { redPoints.style.animation = ''; }, 500);
        }
        
        // Update green zone points
        const greenPoints = document.getElementById('green-zone-points');
        if (greenPoints) {
            greenPoints.textContent = data.points.green || 0;
            // Flash effect when points change
            greenPoints.style.animation = 'pulse 0.5s';
            setTimeout(() => { greenPoints.style.animation = ''; }, 500);
        }
        
        // Update blue zone points
        const bluePoints = document.getElementById('blue-zone-points');
        if (bluePoints) {
            bluePoints.textContent = data.points.blue || 0;
            // Flash effect when points change
            bluePoints.style.animation = 'pulse 0.5s';
            setTimeout(() => { bluePoints.style.animation = ''; }, 500);
        }
    }
    
    // Alternative update method for zone scores
    if (data.action === 'updateZoneScores') {
        console.log('[KOTH ZONE FIX] Updating zone scores:', data);
        
        if (data.red !== undefined) {
            const redPoints = document.getElementById('red-zone-points');
            if (redPoints) redPoints.textContent = data.red;
        }
        
        if (data.green !== undefined) {
            const greenPoints = document.getElementById('green-zone-points');
            if (greenPoints) greenPoints.textContent = data.green;
        }
        
        if (data.blue !== undefined) {
            const bluePoints = document.getElementById('blue-zone-points');
            if (bluePoints) bluePoints.textContent = data.blue;
        }
    }
    
    // Update KOTH zone control status
    if (data.action === 'updateKothZoneControl') {
        console.log('[KOTH ZONE FIX] Updating zone control:', data);
        
        const kothStatus = document.getElementById('koth-zone-status');
        if (kothStatus) {
            // Show the zone status
            kothStatus.style.display = 'block';
            
            // Update the progress text
            const progressText = document.getElementById('koth-progress-text');
            if (progressText) {
                progressText.textContent = data.text || 'Contested';
                progressText.style.color = data.color || '#808080';
            }
            
            // Update progress bar color based on controlling team
            const progressFill = document.getElementById('koth-progress-fill');
            if (progressFill) {
                if (data.controlling === 'red') {
                    progressFill.style.backgroundColor = '#ff0000';
                    progressFill.style.width = '100%';
                } else if (data.controlling === 'blue') {
                    progressFill.style.backgroundColor = '#0064ff';
                    progressFill.style.width = '100%';
                } else if (data.controlling === 'green') {
                    progressFill.style.backgroundColor = '#00ff00';
                    progressFill.style.width = '100%';
                } else {
                    progressFill.style.backgroundColor = '#808080';
                    progressFill.style.width = '50%';
                }
            }
        }
    }
    
    // Hide KOTH zone status when not in zone
    if (data.action === 'hideKothZone') {
        const kothStatus = document.getElementById('koth-zone-status');
        if (kothStatus) {
            kothStatus.style.display = 'none';
        }
    }
});

// Add CSS for pulse animation if not already present
if (!document.getElementById('koth-animations')) {
    const style = document.createElement('style');
    style.id = 'koth-animations';
    style.textContent = `
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
        
        .zone-box {
            transition: all 0.3s ease;
        }
        
        .zone-number {
            font-weight: bold;
            font-size: 24px;
        }
        
        #koth-zone-status {
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        #koth-progress-text {
            font-weight: bold;
            text-align: center;
            margin-bottom: 5px;
        }
        
        .koth-progress-bar {
            width: 200px;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid white;
            border-radius: 10px;
            overflow: hidden;
            margin: 0 auto;
        }
        
        .koth-progress-fill {
            height: 100%;
            transition: all 0.5s ease;
        }
    `;
    document.head.appendChild(style);
}

// Initialize zone points to 0 on load
document.addEventListener('DOMContentLoaded', function() {
    console.log('[KOTH ZONE FIX] Initializing zone points display');
    
    // Set all zone points to 0
    const zonePoints = ['red-zone-points', 'green-zone-points', 'blue-zone-points'];
    zonePoints.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = '0';
        }
    });
    
    // Hide KOTH zone status initially
    const kothStatus = document.getElementById('koth-zone-status');
    if (kothStatus) {
        kothStatus.style.display = 'none';
    }
});

console.log('[KOTH ZONE FIX] Zone HUD script loaded');
