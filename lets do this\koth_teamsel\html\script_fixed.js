// UI state management
let currentMenu = null;
let playerMoney = 0;
let playerLevel = 1;

// Listen for NUI messages from client
window.addEventListener('message', (event) => {
  const data = event.data;
  
  switch(data.action) {
    case 'showTeamSelect':
      showTeamSelect(data.counts);
      break;
    case 'showMenu':
      showMenu(data.type, data.items, data.money, data.playerLevel);
      break;
    case 'showWeaponSelect':
      showWeaponSelect(data.class, data.weapons, data.money);
      break;
    case 'hideAll':
      hideAll();
      break;
    case 'initHUD':
      initHUD(data.playerData);
      break;
    case 'updateHUD':
      updateHUD(data.playerData);
      break;
    case 'updateHealth':
      updateHealth(data.health);
      break;
    case 'updateTeamCounts':
      updateTeamCounts(data.counts);
      break;
    case 'updateZonePoints':
      updateZonePoints(data.points);
      break;
    case 'updateKothZone':
      updateKothZone(data.zoneData);
      break;
    case 'hideKothZone':
      hideKothZone();
      break;
    case 'showDeathScreen':
      showDeathScreen(data.killer, data.killerId);
      break;
    case 'hideDeathScreen':
      hideDeathScreen();
      break;
    case 'showKillReward':
      showKillReward(data);
      break;
    case 'showLevelUp':
      showLevelUp(data);
      break;
  }
});

// Show team selection screen
function showTeamSelect(counts) {
  hideAll();
  document.getElementById('team-select').style.display = 'block';
  document.getElementById('overlay').style.display = 'block';
  
  // Update team counts
  if (counts) {
    document.getElementById('count-red').textContent = counts.red || 0;
    document.getElementById('count-blue').textContent = counts.blue || 0;
    document.getElementById('count-green').textContent = counts.green || 0;
  }
  
  // Hide loading indicator
  document.querySelector('.loading-indicator').style.display = 'none';
}

// Team selection handlers
document.querySelectorAll('.team-card').forEach(card => {
  card.addEventListener('click', function() {
    const team = this.dataset.team;
    fetch(`https://${GetParentResourceName()}/selectTeam`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ team })
    });
  });
});

// Show menu (vehicles or classes)
function showMenu(type, items, money, level) {
  hideAll();
  currentMenu = type;
  playerMoney = money || 0;
  playerLevel = level || 1;
  
  if (type === 'vehicles') {
    showVehicleMenu(items, money);
  } else if (type === 'classes') {
    showClassMenu(items, money, level);
  }
}

// Show vehicle menu
function showVehicleMenu(vehicles, money) {
  document.getElementById('vehicles-shop').style.display = 'block';
  document.getElementById('overlay').style.display = 'block';
  
  // Update money display
  document.getElementById('vehicle-player-money').textContent = money || 0;
  
  // Clear and populate vehicles
  const grid = document.getElementById('vehicles-grid');
  grid.innerHTML = '';
  
  vehicles.forEach(vehicle => {
    const vehicleCard = createVehicleCard(vehicle, money);
    grid.appendChild(vehicleCard);
  });
}

// Create vehicle card
function createVehicleCard(vehicle, playerMoney) {
  const div = document.createElement('div');
  div.className = 'vehicle-card';
  
  const canAffordBuy = playerMoney >= vehicle.cost;
  const canAffordRent = playerMoney >= vehicle.rent;
  
  div.innerHTML = `
    <div class="vehicle-image">
      <img src="${vehicle.img || 'images/vehicles/default.png'}" alt="${vehicle.name}">
    </div>
    <div class="vehicle-info">
      <h3>${vehicle.name}</h3>
      <div class="vehicle-prices">
        <button class="buy-btn ${!canAffordBuy ? 'disabled' : ''}" 
                onclick="buyVehicle('${vehicle.name}', ${vehicle.cost})"
                ${!canAffordBuy ? 'disabled' : ''}>
          Buy $${vehicle.cost.toLocaleString()}
        </button>
        <button class="rent-btn ${!canAffordRent ? 'disabled' : ''}" 
                onclick="rentVehicle('${vehicle.name}', ${vehicle.rent})"
                ${!canAffordRent ? 'disabled' : ''}>
          Rent $${vehicle.rent.toLocaleString()}
        </button>
      </div>
    </div>
  `;
  
  return div;
}

// Vehicle purchase functions
function buyVehicle(name, cost) {
  if (playerMoney >= cost) {
    fetch(`https://${GetParentResourceName()}/buyVehicle`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ name, cost })
    });
  }
}

function rentVehicle(name, rent) {
  if (playerMoney >= rent) {
    fetch(`https://${GetParentResourceName()}/rentVehicle`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ name, rent })
    });
  }
}

// Show class menu
function showClassMenu(classes, money, level) {
  document.getElementById('classes-selection').style.display = 'block';
  document.getElementById('overlay').style.display = 'block';
  
  // Clear and populate classes
  const container = document.getElementById('classes-container');
  container.innerHTML = '';
  
  classes.forEach(classItem => {
    const classCard = createClassCard(classItem, level);
    container.appendChild(classCard);
  });
}

// Create class card
function createClassCard(classItem, playerLevel) {
  const div = document.createElement('div');
  div.className = `class-card ${classItem.locked ? 'locked' : ''}`;
  
  div.innerHTML = `
    <img src="${classItem.img || 'images/classes/default.png'}" alt="${classItem.name}">
    <h3>${classItem.name}</h3>
    <p>${classItem.unlock}</p>
  `;
  
  if (!classItem.locked) {
    div.onclick = () => selectClass(classItem.id);
  }
  
  return div;
}

// Select class
function selectClass(classId) {
  fetch(`https://${GetParentResourceName()}/selectClass`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ id: classId })
  });
}

// Show weapon selection
function showWeaponSelect(classId, weapons, money) {
  hideAll();
  document.getElementById('weapons-shop').style.display = 'block';
  document.getElementById('overlay').style.display = 'block';
  
  // Update money display
  document.getElementById('player-money').textContent = money || 0;
  
  // Clear and populate weapons
  const grid = document.getElementById('weapons-grid');
  grid.innerHTML = '';
  
  weapons.forEach(weapon => {
    const weaponCard = createWeaponCard(weapon, money, classId);
    grid.appendChild(weaponCard);
  });
}

// Create weapon card
function createWeaponCard(weapon, playerMoney, classId) {
  const div = document.createElement('div');
  div.className = 'weapon-card';
  
  const canAfford = playerMoney >= weapon.price;
  
  div.innerHTML = `
    <div class="weapon-image">
      <img src="${weapon.img || 'images/guns/unarmed.png'}" alt="${weapon.name}">
    </div>
    <div class="weapon-info">
      <h3>${weapon.name}</h3>
      <button class="weapon-buy-btn ${!canAfford ? 'disabled' : ''}" 
              onclick="selectWeapon('${weapon.weapon}', '${classId}', ${weapon.price})"
              ${!canAfford ? 'disabled' : ''}>
        $${weapon.price.toLocaleString()}
      </button>
    </div>
  `;
  
  return div;
}

// Select weapon
function selectWeapon(weapon, classId, price) {
  fetch(`https://${GetParentResourceName()}/selectWeapon`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ weapon, class: classId, price })
  });
}

// Close buttons
document.getElementById('vehicle-shop-close').addEventListener('click', closeMenu);
document.getElementById('shop-close').addEventListener('click', closeMenu);
document.getElementById('classes-close').addEventListener('click', closeMenu);
document.getElementById('close-btn').addEventListener('click', closeMenu);

function closeMenu() {
  fetch(`https://${GetParentResourceName()}/closeMenu`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({})
  });
}

// Hide all UI elements
function hideAll() {
  document.getElementById('overlay').style.display = 'none';
  document.getElementById('team-select').style.display = 'none';
  document.getElementById('menu-container').style.display = 'none';
  document.getElementById('weapons-shop').style.display = 'none';
  document.getElementById('vehicles-shop').style.display = 'none';
  document.getElementById('classes-selection').style.display = 'none';
}

// Initialize HUD
function initHUD(playerData) {
  document.getElementById('game-hud').style.display = 'block';
  if (playerData) {
    updateHUD(playerData);
  }
}

// Update HUD with real player data
function updateHUD(playerData) {
  if (!playerData) return;
  
  // Update player name
  if (playerData.name) {
    document.getElementById('player-name').textContent = playerData.name;
  }
  
  // Update money
  if (playerData.money !== undefined) {
    document.getElementById('player-money-display').textContent = `$${playerData.money.toLocaleString()}`;
  }
  
  // Update level
  if (playerData.level !== undefined) {
    document.getElementById('player-level').textContent = playerData.level;
  }
  
  // Update XP (calculate XP for next level)
  if (playerData.xp !== undefined && playerData.level !== undefined) {
    const xpLevels = [0, 100, 250, 500, 1000, 1750, 2750, 4000, 6000, 8500, 12000, 16000, 21000, 27000, 35000, 45000, 57000, 72000, 90000, 112000];
    const currentLevelXP = xpLevels[playerData.level - 1] || 0;
    const nextLevelXP = xpLevels[playerData.level] || 999999;
    const xpProgress = playerData.xp - currentLevelXP;
    const xpNeeded = nextLevelXP - currentLevelXP;
    document.getElementById('player-xp').textContent = `${xpProgress}/${xpNeeded}`;
  }
}

// Update health bar
function updateHealth(health) {
  const healthFill = document.getElementById('health-fill');
  const healthText = document.getElementById('health-text');
  
  healthFill.style.width = `${health}%`;
  healthText.textContent = Math.floor(health);
  
  // Change color based on health
  if (health > 60) {
    healthFill.style.backgroundColor = '#4CAF50';
  } else if (health > 30) {
    healthFill.style.backgroundColor = '#ff9800';
  } else {
    healthFill.style.backgroundColor = '#f44336';
  }
}

// Update team counts
function updateTeamCounts(counts) {
  if (counts) {
    document.getElementById('red-players').textContent = counts.red || 0;
    document.getElementById('green-players').textContent = counts.green || 0;
    document.getElementById('blue-players').textContent = counts.blue || 0;
  }
}

// Update zone points
function updateZonePoints(points) {
  if (points) {
    document.getElementById('red-zone-points').textContent = points.red || 0;
    document.getElementById('green-zone-points').textContent = points.green || 0;
    document.getElementById('blue-zone-points').textContent = points.blue || 0;
  }
}

// Update KOTH zone status
function updateKothZone(zoneData) {
  const zoneStatus = document.getElementById('koth-zone-status');
  zoneStatus.style.display = 'block';
  
  const progressFill = document.getElementById('koth-progress-fill');
  const progressText = document.getElementById('koth-progress-text');
  
  // Update progress bar
  const progress = (zoneData.progress / zoneData.threshold) * 100;
  progressFill.style.width = `${progress}%`;
  
  // Update text and colors
  if (zoneData.contested) {
    progressText.textContent = 'Contested';
    progressFill.style.backgroundColor = '#ff9800';
  } else if (zoneData.dominant) {
    progressText.textContent = `${zoneData.dominant.toUpperCase()} Capturing`;
    const colors = { red: '#f44336', blue: '#2196F3', green: '#4CAF50' };
    progressFill.style.backgroundColor = colors[zoneData.dominant] || '#9e9e9e';
  } else if (zoneData.controlling) {
    progressText.textContent = `${zoneData.controlling.toUpperCase()} Controlled`;
    const colors = { red: '#f44336', blue: '#2196F3', green: '#4CAF50' };
    progressFill.style.backgroundColor = colors[zoneData.controlling] || '#9e9e9e';
  } else {
    progressText.textContent = 'Neutral';
    progressFill.style.backgroundColor = '#9e9e9e';
  }
}

// Hide KOTH zone status
function hideKothZone() {
  document.getElementById('koth-zone-status').style.display = 'none';
}

// Show death screen
function showDeathScreen(killerName, killerId) {
  document.getElementById('death-screen').style.display = 'flex';
  document.getElementById('killer-name').textContent = killerName || 'Unknown';
  document.getElementById('killer-id').textContent = killerId || '0';
  
  // Start bleedout timer
  let bleedoutTime = 50;
  const timerElement = document.getElementById('bleedout-timer');
  
  const bleedoutInterval = setInterval(() => {
    bleedoutTime--;
    timerElement.textContent = bleedoutTime;
    
    if (bleedoutTime <= 0) {
      clearInterval(bleedoutInterval);
    }
  }, 1000);
  
  // Store interval to clear later
  window.bleedoutInterval = bleedoutInterval;
}

// Hide death screen
function hideDeathScreen() {
  document.getElementById('death-screen').style.display = 'none';
  
  // Clear bleedout timer
  if (window.bleedoutInterval) {
    clearInterval(window.bleedoutInterval);
  }
  
  // Reset respawn progress
  document.getElementById('respawn-fill').style.width = '0%';
}

// Show kill reward popup
function showKillReward(data) {
  const popup = document.getElementById('kill-reward-popup');
  document.getElementById('kill-reward-value').textContent = `$${data.money}`;
  document.getElementById('kill-xp-value').textContent = data.xp;
  
  popup.style.display = 'block';
  popup.style.animation = 'slideInRight 0.5s ease-out';
  
  // Hide after 3 seconds
  setTimeout(() => {
    popup.style.animation = 'slideOutRight 0.5s ease-out';
    setTimeout(() => {
      popup.style.display = 'none';
    }, 500);
  }, 3000);
}

// Show level up popup
function showLevelUp(data) {
  const popup = document.getElementById('levelup-popup');
  document.getElementById('level-old').textContent = data.oldLevel;
  document.getElementById('level-new').textContent = data.newLevel;
  
  popup.style.display = 'block';
  popup.style.animation = 'bounceIn 0.5s ease-out';
  
  // Hide after 4 seconds
  setTimeout(() => {
    popup.style.animation = 'fadeOut 0.5s ease-out';
    setTimeout(() => {
      popup.style.display = 'none';
    }, 500);
  }, 4000);
}

// Search functionality
document.getElementById('weapon-search').addEventListener('input', function(e) {
  const searchTerm = e.target.value.toLowerCase();
  const cards = document.querySelectorAll('#weapons-grid .weapon-card');
  
  cards.forEach(card => {
    const name = card.querySelector('h3').textContent.toLowerCase();
    card.style.display = name.includes(searchTerm) ? 'block' : 'none';
  });
});

document.getElementById('vehicle-search').addEventListener('input', function(e) {
  const searchTerm = e.target.value.toLowerCase();
  const cards = document.querySelectorAll('#vehicles-grid .vehicle-card');
  
  cards.forEach(card => {
    const name = card.querySelector('h3').textContent.toLowerCase();
    card.style.display = name.includes(searchTerm) ? 'block' : 'none';
  });
});

// Helper function for resource name
function GetParentResourceName() {
  return window.GetParentResourceName ? window.GetParentResourceName() : 'koth_teamsel';
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
  // Hide all UI elements on load
  hideAll();
  document.getElementById('game-hud').style.display = 'none';
  document.getElementById('death-screen').style.display = 'none';
  document.getElementById('kill-reward-popup').style.display = 'none';
  document.getElementById('levelup-popup').style.display = 'none';
});
