# KOTH Admin Panel

A comprehensive admin panel for managing the KOTH (King of the Hill) game mode.

## Features

- **Player Management**
  - View all online players with their stats
  - Give money to players
  - Give XP to players
  - Set player levels
  - Reset player stats
  - Search/filter players

- **KOTH Round Control**
  - Start new KOTH rounds
  - Stop current rounds
  - View zone control status
  - Monitor team points

## Installation

1. Place the `koth_admin` folder in your server's resources directory
2. Add `ensure koth_admin` to your server.cfg AFTER `ensure koth_teamsel`
3. Restart your server

## Configuration

### Setting Up Admin Access

By default, the admin check is set to allow everyone (for testing). You MUST configure this for production use.

Edit `server.lua` and modify the `isAdmin` function:

```lua
local function isAdmin(source)
    local identifiers = GetPlayerIdentifiers(source)
    
    -- Add your admin identifiers here
    local adminIdentifiers = {
        "steam:110000112345678",  -- Replace with your Steam ID
        "license:abc123def456",    -- Replace with your license
        "discord:123456789"        -- Replace with your Discord ID
    }
    
    for _, id in ipairs(identifiers) do
        for _, adminId in ipairs(adminIdentifiers) do
            if id == adminId then
                return true
            end
        end
    end
    
    return false  -- Make sure this is false for production!
end
```

### Alternative: Using ACE Permissions

If you use ACE permissions, you can replace the `isAdmin` function with:

```lua
local function isAdmin(source)
    return IsPlayerAceAllowed(source, "koth.admin")
end
```

Then add to your server.cfg:
```
add_ace group.admin koth.admin allow
```

## Usage

1. Use the command `/kothadmin` in-game to open the admin panel
2. Click on any player to view their detailed stats and perform actions
3. Use the KOTH Round Control section to manage rounds

## Commands

- `/kothadmin` - Opens the admin panel (admin only)

## Dependencies

- `koth_teamsel` - The main KOTH game mode resource
- `oxmysql` - For database operations

## Notes

- All admin actions are logged to the server console
- Money transactions are logged to the database
- The panel auto-refreshes when actions are performed
- Press ESC to close the panel or any modal

## Troubleshooting

If the admin panel doesn't open:
1. Check that you have admin permissions configured correctly
2. Ensure both `koth_teamsel` and `koth_admin` resources are running
3. Check the F8 console for any errors

## Future Features

- Ban/kick players
- Teleport to players
- Multiple map support
- Match history
- Detailed statistics
- Admin action logs viewer
